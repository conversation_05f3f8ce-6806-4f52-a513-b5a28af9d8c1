# Khắc phục lỗi "OpenSSL unable to validate key"

## <PERSON><PERSON> tả lỗi
Lỗi "OpenSSL unable to validate key" x<PERSON>y ra khi private key trong JSON credentials của Google Service Account bị hỏng hoặc không đúng format.

## Nguyên nhân phổ biến
1. **Private key bị hỏng khi copy-paste**: Khi copy JSON từ trình duyệt hoặc editor, cá<PERSON> ký tự đặc biệt có thể bị thay đổi
2. **Escape characters bị double-escape**: `\n` trở thành `\\n`
3. **Header/footer bị thiếu dấu gạch ngang**: `BEGIN PRIVATE KEY` thay vì `-----BEGIN PRIVATE KEY-----`
4. **Có ký tự Unicode hoặc BOM**: Ký tự không nhìn thấy được làm hỏng format

## Cách khắc phục

### Bước 1: Tạo lại Service Account (Khuyến nghị)
1. <PERSON><PERSON><PERSON> [Google Cloud Console](https://console.cloud.google.com/)
2. Chọn project của bạn
3. Vào **IAM & Admin** → **Service Accounts**
4. Tạo Service Account mới hoặc tạo key mới cho Service Account hiện tại
5. Tải xuống file JSON (KHÔNG copy-paste từ trình duyệt)

### Bước 2: Copy JSON đúng cách
1. Mở file JSON vừa tải xuống bằng **Notepad** (Windows) hoặc **TextEdit** (Mac)
2. Sử dụng **Ctrl+A** để chọn tất cả
3. **Ctrl+C** để copy
4. Paste vào trường JSON trong plugin

### Bước 3: Sử dụng công cụ chẩn đoán
1. Vào trang quản lý plugin
2. Nhấn nút **"🔍 Chẩn đoán Chi tiết"**
3. Kiểm tra kết quả chẩn đoán private key

### Bước 4: Nếu vẫn lỗi
1. Thử bật tùy chọn **"Tắt xác minh SSL"** tạm thời để test
2. Nếu hoạt động, vấn đề là SSL certificate
3. Tải xuống file `cacert.pem` và đặt vào thư mục `certs/` của plugin

## Các cải tiến trong version 1.3.7

### Hàm xử lý JSON mới
- `choacc_sheets_parse_credentials_json()`: Thử nhiều phương pháp parse JSON
- `choacc_sheets_fix_private_key()`: Tự động sửa format private key
- `choacc_sheets_auto_fix_common_private_key_issues()`: Sửa các lỗi phổ biến
- `choacc_sheets_test_private_key()`: Kiểm tra OpenSSL có đọc được private key không

### Chẩn đoán chi tiết hơn
- Kiểm tra format header/footer của private key
- Kiểm tra độ dài private key
- Test OpenSSL validation
- Hướng dẫn khắc phục cụ thể

## Lưu ý quan trọng
- **KHÔNG** sử dụng tùy chọn "Tắt xác minh SSL" trong môi trường production
- Luôn tạo Service Account mới nếu private key bị hỏng
- Đảm bảo Service Account có quyền truy cập vào Google Sheet

## Liên hệ hỗ trợ
Nếu vẫn gặp vấn đề, vui lòng liên hệ qua Fanpage để được hỗ trợ trực tiếp.
