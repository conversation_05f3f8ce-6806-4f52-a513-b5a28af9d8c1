name: Test Suite
on:
  push:
    branches:
      - main
  pull_request:

jobs:
    test:
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                os: [ubuntu-24.04]
                php: [ "8.0", "8.1", "8.2", "8.3", "8.4" ]
                include:
                  - os: macos-latest
                    php: "8.1"
        name: PHP ${{matrix.php }} Unit Test
        steps:
            - uses: actions/checkout@v4
            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                php-version: ${{ matrix.php }}
            - name: Install Dependencies
              uses: nick-invision/retry@v3
              with:
                timeout_minutes: 10
                max_attempts: 3
                command: composer install
            - name: Run Script
              run: vendor/bin/phpunit
    generator:
        runs-on: ubuntu-24.04
        name: Generator Unit Tests
        steps:
            - uses: actions/checkout@v4
            - name: Set up Python
              uses: actions/setup-python@v5
              with:
                python-version: "3.13"
            - name: Install dependencies
              run: |
                python -m pip install setuptools
                python -m pip install -e generator/ --user
            - name: Run Tests
              run: |
                bash generator/run_tests.sh
    casing:
        runs-on: macos-latest
        strategy:
            matrix:
                php: [ "8.1" ]
        name: Casing Conflict Test
        steps:
            - uses: actions/checkout@v4
            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                php-version: ${{ matrix.php }}
            - name: Run Script
              run: |
                set +e
                git diff-index --quiet HEAD --
                if [ $? -ne 0 ]; then
                  git status;
                  exit 1
                fi;
