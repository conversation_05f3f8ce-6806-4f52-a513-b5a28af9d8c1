# Hướng dẫn nhập Service Account từng trường riêng biệt

## Tại sao nên sử dụng phương pháp này?

Phương pháp nhập từng trường riêng biệt giúp:
- ✅ **Tr<PERSON>h lỗi "OpenSSL unable to validate key"** 
- ✅ **Không bị lỗi format khi copy-paste JSON**
- ✅ **<PERSON><PERSON> kiểm tra và sửa lỗi từng trường**
- ✅ **Tránh vấn đề escape characters**

## Bước 1: L<PERSON>y thông tin từ file JSON

1. Mở file JSON Service Account bạn đã tải từ Google Cloud Console
2. Tìm các trường sau trong file JSON:

```json
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## Bước 2: Nhập từng trường vào plugin

### 2.1. Project ID
- **Trường:** `project_id`
- **Ví dụ:** `loyal-network-465105-p1`
- **Nhập vào:** Trường "Project ID"

### 2.2. Private Key ID
- **Trường:** `private_key_id`
- **Ví dụ:** `d3e50abc123def456...`
- **Nhập vào:** Trường "Private Key ID"

### 2.3. Private Key (QUAN TRỌNG NHẤT)
- **Trường:** `private_key`
- **Lưu ý:** Đây là trường dễ gây lỗi nhất
- **Cách xử lý:**
  1. Copy toàn bộ giá trị của `private_key` (bao gồm dấu ngoặc kép)
  2. Loại bỏ dấu ngoặc kép ở đầu và cuối
  3. Thay thế `\n` bằng xuống dòng thật
  4. Kết quả cuối cùng phải có dạng:
  ```
  -----BEGIN PRIVATE KEY-----
  MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
  (nhiều dòng base64)
  -----END PRIVATE KEY-----
  ```

### 2.4. Client Email
- **Trường:** `client_email`
- **Ví dụ:** `<EMAIL>`
- **Nhập vào:** Trường "Client Email"

### 2.5. Client ID
- **Trường:** `client_id`
- **Ví dụ:** `123456789012345678901`
- **Nhập vào:** Trường "Client ID"

### 2.6. Client X509 Cert URL
- **Trường:** `client_x509_cert_url`
- **Ví dụ:** `https://www.googleapis.com/robot/v1/metadata/x509/tichhoptudong%40loyal-network-465105-p1.iam.gserviceaccount.com`
- **Nhập vào:** Trường "Client X509 Cert URL"

## Bước 3: Kiểm tra và lưu

1. **Điền đầy đủ tất cả các trường bắt buộc:**
   - Project ID
   - Private Key ID  
   - Private Key
   - Client Email
   - Client ID
   - Client X509 Cert URL

2. **Nhấn "Lưu Cài đặt"**

3. **Chạy "🔍 Chẩn đoán Chi tiết"** để kiểm tra

## Xử lý Private Key đúng cách

### Ví dụ Private Key trong JSON:
```json
"private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"
```

### Cách chuyển đổi:
1. **Bước 1:** Copy giá trị (bỏ dấu ngoặc kép):
   ```
   -----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n
   ```

2. **Bước 2:** Thay thế `\n` bằng xuống dòng thật:
   ```
   -----BEGIN PRIVATE KEY-----
   MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
   -----END PRIVATE KEY-----
   ```

3. **Bước 3:** Paste vào trường "Private Key"

## Lưu ý quan trọng

- ⚠️ **KHÔNG** để trống bất kỳ trường nào
- ⚠️ **KHÔNG** thêm dấu ngoặc kép vào các trường
- ⚠️ **KHÔNG** thêm dấu phẩy ở cuối
- ✅ **LUÔN** kiểm tra Private Key có đúng format (có header và footer)
- ✅ **LUÔN** chạy chẩn đoán sau khi lưu

## Khắc phục sự cố

### Nếu vẫn gặp lỗi:
1. **Kiểm tra lại Private Key:** Đảm bảo không có ký tự thừa
2. **Tạo lại Service Account:** Tạo key mới từ Google Cloud Console
3. **Sử dụng editor khác:** Thử dùng Notepad++ hoặc VS Code để xử lý Private Key
4. **Liên hệ hỗ trợ:** Nếu vẫn không được, gửi screenshot lỗi qua Fanpage
