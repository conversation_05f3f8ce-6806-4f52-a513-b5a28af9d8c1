# Hướng dẫn Khắc phục Lỗi "Liên kết bạn theo dõi đã hết hạn"

## Tổng quan
Lỗi "Liên kết bạn theo dõi đã hết hạn" thường xuất hiện khi có vấn đề với xác thực Google Sheets API. Dưới đây là các bước để chẩn đoán và khắc phục.

## Bước 1: Sử dụng Công cụ Chẩn đoán Tích hợp

1. Truy cập **WooCommerce > Tài khoản Google Sheet** trong WordPress Admin
2. Nhấn nút **🔍 Chẩn đoán Chi tiết** (màu xanh)
3. Xem kết quả chẩn đoán để xác định vấn đề cụ thể

## Bước 2: <PERSON><PERSON><PERSON>uyên nhân Phổ biến và Cách Khắc phục

### 1. Service Account chư<PERSON> đượ<PERSON> chia sẻ quyền truy cập

**Triệu chứng:** Lỗi "The caller does not have permission" hoặc "Requested entity was not found"

**C<PERSON>ch khắc phục:**
1. Mở Google Sheet của bạn
2. Nhấn nút **Chia sẻ** (Share)
3. Thêm email service account: `<EMAIL>`
4. Cấp quyền **Editor** (Chỉnh sửa)
5. Nhấn **Gửi**

### 2. Google Sheet ID không chính xác

**Triệu chứng:** Lỗi "Requested entity was not found"

**Cách khắc phục:**
1. Mở Google Sheet trong trình duyệt
2. Sao chép ID từ URL: `https://docs.google.com/spreadsheets/d/[SHEET_ID]/edit`
3. Dán vào trường **Google Sheet ID** trong cài đặt plugin

### 3. Google Sheets API chưa được kích hoạt

**Triệu chứng:** Lỗi "Google Sheets API has not been used"

**Cách khắc phục:**
1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Chọn project: `loyal-network-465105-p1`
3. Vào **APIs & Services > Library**
4. Tìm và kích hoạt **Google Sheets API**

### 4. Credentials JSON không hợp lệ

**Triệu chứng:** Lỗi "Invalid JSON" hoặc "Missing required fields"

**Cách khắc phục:**
1. Kiểm tra file JSON có đầy đủ các trường:
   - `type`
   - `project_id`
   - `private_key`
   - `client_email`
2. Đảm bảo JSON không bị lỗi cú pháp
3. Sao chép lại toàn bộ nội dung từ file JSON gốc

## Bước 3: Kiểm tra Cấu trúc Google Sheet

Đảm bảo Google Sheet có cấu trúc đúng:

| A (ID) | B (Username) | C (Password) | D (Login_URL) | E (Status) | F (Order_ID) | G (Sold_Date) |
|--------|--------------|--------------|---------------|------------|--------------|---------------|
| ACC001 | user1        | pass1        | https://...   | Available  |              |               |
| ACC002 | user2        | pass2        | https://...   | Available  |              |               |

## Bước 4: Kiểm tra Log Lỗi

1. Truy cập **WordPress Admin > Tools > Site Health > Info > Server**
2. Hoặc kiểm tra file log tại `/wp-content/debug.log`
3. Tìm các dòng có chứa "ChoAcc.com Sheets"

## Bước 5: Test Kết nối

1. Trong trang cài đặt plugin, nhấn **Kiểm tra Kết nối API**
2. Nếu thành công, test từng sản phẩm bằng nút **Test** trong bảng quản lý sản phẩm

## Liên hệ Hỗ trợ

Nếu vẫn gặp vấn đề, vui lòng cung cấp:
1. Kết quả chẩn đoán chi tiết
2. Screenshot lỗi
3. Nội dung log lỗi (nếu có)
