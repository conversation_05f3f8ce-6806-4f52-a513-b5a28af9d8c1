<?php
/**
 * Plugin Name: ChoAcc.com - Google Sheets Integration
 * Plugin URI:  https://choacc.com/
 * Description: Tự động phân phối tài khoản từ Google Sheets qua email khi đơn hàng WooCommerce hoàn thành, với trang quản lý tập trung.
 * Version:     1.4.0
 * Author:      ChoAcc.com
 * Author URI:  https://yourwebsite.com
 * License:     GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: choacc-sheets
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Định nghĩa đường dẫn đến thư mục vendor (nơi chứa Google API Client Library)
define( 'CHOACC_SHEETS_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'CHOACC_SHEETS_VENDOR_DIR', CHOACC_SHEETS_PLUGIN_DIR . 'vendor/' );

// <PERSON><PERSON>m tra xem thư viện Google API Client đã được tải chưa
if ( file_exists( CHOACC_SHEETS_VENDOR_DIR . 'autoload.php' ) ) {
    require_once CHOACC_SHEETS_VENDOR_DIR . 'autoload.php';
} else {
    // Thông báo lỗi nếu thư viện không tìm thấy (quan trọng!)
    add_action( 'admin_notices', function() {
        echo '<div class="notice notice-error"><p><strong>ChoAcc.com Google Sheets Integration:</strong> Thư viện Google API Client không tìm thấy. Vui lòng chạy `composer install` trong thư mục plugin hoặc tải thư mục `vendor` lên.</p></div>';
    });
    return; // Dừng kích hoạt plugin nếu không có thư viện
}

/**
 * Cấu hình plugin: Lấy từ tùy chọn WordPress hoặc giá trị mặc định
 */
function choacc_sheets_get_config() {
    $default_config = array(
        'spreadsheet_id'                   => '',
        // Thay đổi: Tách các trường Service Account riêng biệt
        'service_account_type'             => 'service_account',
        'service_account_project_id'       => '',
        'service_account_private_key_id'   => '',
        'service_account_private_key'      => '',
        'service_account_client_email'     => '',
        'service_account_client_id'        => '',
        'service_account_auth_uri'         => 'https://accounts.google.com/o/oauth2/auth',
        'service_account_token_uri'        => 'https://oauth2.googleapis.com/token',
        'service_account_auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
        'service_account_client_x509_cert_url' => '',
        // Giữ lại để backward compatibility
        'service_account_credentials_json' => '',
        'columns'                          => [
            'ID'         => 0, // Cột A (index 0)
            'Username'   => 1, // Cột B (index 1)
            'Password'   => 2, // Cột C (index 2)
            'Login_URL'  => 3, // Cột D (index 3)
            'Status'     => 4, // Cột E (index 4)
            'Order_ID'   => 5, // Cột F (index 5)
            'Sold_Date'  => 6, // Cột G (index 6)
        ],
        'status_available'                 => 'Available',
        'status_sold'                      => 'Sold',
        'product_account_id_meta_key'      => '_choacc_sheets_account_id',
        'product_sheet_tab_meta_key'       => '_choacc_sheets_tab_name',
        'shop_name'                        => 'ChoAcc.com',
        'shop_logo_url'                    => 'http://choacc.com/wp-content/uploads/2025/07/Tosca-Orange-Modern-Online-Shop-Logo-3.png',
        'support_url'                      => 'https://www.facebook.com/taikhoansieurecom',
        'support_text'                     => 'Fanpage của chúng tôi',
        'disable_ssl_verify'               => false, // Mặc định là FALSE (không tắt xác minh SSL)
    );

    $saved_settings = get_option( 'choacc_sheets_settings', array() );
    return array_merge( $default_config, $saved_settings );
}

/**
 * Hàm build credentials từ các trường riêng biệt
 */
function choacc_sheets_build_credentials_from_fields($config) {
    // Kiểm tra xem có sử dụng trường riêng biệt không
    if (!empty($config['service_account_project_id']) && !empty($config['service_account_private_key']) && !empty($config['service_account_client_email'])) {
        // Sử dụng các trường riêng biệt
        $credentials = [
            'type' => $config['service_account_type'],
            'project_id' => $config['service_account_project_id'],
            'private_key_id' => $config['service_account_private_key_id'],
            'private_key' => $config['service_account_private_key'],
            'client_email' => $config['service_account_client_email'],
            'client_id' => $config['service_account_client_id'],
            'auth_uri' => $config['service_account_auth_uri'],
            'token_uri' => $config['service_account_token_uri'],
            'auth_provider_x509_cert_url' => $config['service_account_auth_provider_x509_cert_url'],
            'client_x509_cert_url' => $config['service_account_client_x509_cert_url']
        ];

        error_log('ChoAcc.com Sheets Debug: Using individual fields for credentials');
        return $credentials;
    }

    // Fallback: Sử dụng JSON cũ nếu có
    if (!empty($config['service_account_credentials_json'])) {
        error_log('ChoAcc.com Sheets Debug: Using legacy JSON credentials');
        return choacc_sheets_parse_credentials_json($config['service_account_credentials_json']);
    }

    error_log('ChoAcc.com Sheets Error: No credentials found (neither individual fields nor JSON)');
    return false;
}

/**
 * Hàm parse JSON credentials một cách an toàn (giữ lại để backward compatibility)
 */
function choacc_sheets_parse_credentials_json($raw_json) {
    // Thử các phương pháp parse khác nhau
    $methods = [
        // Phương pháp 1: Parse trực tiếp
        function($json) { return json_decode($json, true); },

        // Phương pháp 2: Stripslashes trước khi parse
        function($json) { return json_decode(stripslashes($json), true); },

        // Phương pháp 3: Loại bỏ các escape characters thừa
        function($json) {
            $cleaned = str_replace(['\\n', '\\r', '\\t'], ["\n", "\r", "\t"], $json);
            return json_decode($cleaned, true);
        },

        // Phương pháp 4: Kết hợp stripslashes và clean escape
        function($json) {
            $stripped = stripslashes($json);
            $cleaned = str_replace(['\\n', '\\r', '\\t'], ["\n", "\r", "\t"], $stripped);
            return json_decode($cleaned, true);
        }
    ];

    foreach ($methods as $index => $method) {
        $result = $method($raw_json);
        if (json_last_error() === JSON_ERROR_NONE && is_array($result)) {
            error_log('ChoAcc.com Sheets Debug: JSON parsed successfully using method ' . ($index + 1));
            return $result;
        }
    }

    error_log('ChoAcc.com Sheets Error: All JSON parsing methods failed. Last error: ' . json_last_error_msg());
    return false;
}

/**
 * Hàm sửa private key để đảm bảo format đúng
 */
function choacc_sheets_fix_private_key($credentials) {
    if (!isset($credentials['private_key'])) {
        return $credentials;
    }

    $private_key = $credentials['private_key'];
    $original_key = $private_key;

    // Bước 1: Áp dụng auto-fix cho các lỗi phổ biến
    $private_key = choacc_sheets_auto_fix_common_private_key_issues($private_key);

    // Bước 2: Loại bỏ các khoảng trắng thừa ở đầu và cuối
    $private_key = trim($private_key);

    // Bước 3: Đảm bảo có header và footer đúng
    $has_begin = strpos($private_key, '-----BEGIN PRIVATE KEY-----') !== false;
    $has_end = strpos($private_key, '-----END PRIVATE KEY-----') !== false;

    if (!$has_begin) {
        error_log('ChoAcc.com Sheets Warning: Private key missing BEGIN header');
        // Thử tìm và sửa các header khác
        if (strpos($private_key, 'BEGIN PRIVATE KEY') !== false) {
            $private_key = str_replace('BEGIN PRIVATE KEY', '-----BEGIN PRIVATE KEY-----', $private_key);
        }
    }

    if (!$has_end) {
        error_log('ChoAcc.com Sheets Warning: Private key missing END footer');
        // Thử tìm và sửa các footer khác
        if (strpos($private_key, 'END PRIVATE KEY') !== false) {
            $private_key = str_replace('END PRIVATE KEY', '-----END PRIVATE KEY-----', $private_key);
        }
    }

    // Bước 4: Đảm bảo format newline đúng
    // Tách header, body và footer
    $lines = explode("\n", $private_key);
    $cleaned_lines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line)) {
            $cleaned_lines[] = $line;
        }
    }

    // Bước 5: Tái tạo private key với format đúng
    if (!empty($cleaned_lines)) {
        $private_key = implode("\n", $cleaned_lines);

        // Đảm bảo có newline sau header và trước footer
        $private_key = str_replace('-----BEGIN PRIVATE KEY-----', "-----BEGIN PRIVATE KEY-----\n", $private_key);
        $private_key = str_replace('-----END PRIVATE KEY-----', "\n-----END PRIVATE KEY-----", $private_key);

        // Loại bỏ newline thừa
        $private_key = preg_replace('/\n{3,}/', "\n\n", $private_key);
        $private_key = trim($private_key);
    }

    // Bước 6: Kiểm tra cuối cùng
    if (strlen($private_key) < 100) {
        error_log('ChoAcc.com Sheets Error: Private key quá ngắn sau khi xử lý (' . strlen($private_key) . ' ký tự)');
        error_log('ChoAcc.com Sheets Debug: Original key length: ' . strlen($original_key));
        error_log('ChoAcc.com Sheets Debug: Processed key length: ' . strlen($private_key));
    }

    $credentials['private_key'] = $private_key;

    error_log('ChoAcc.com Sheets Debug: Private key processed and cleaned (length: ' . strlen($private_key) . ')');

    return $credentials;
}

/**
 * Hàm kiểm tra private key có thể được OpenSSL đọc được không
 */
function choacc_sheets_test_private_key($private_key) {
    // Kiểm tra xem OpenSSL có thể đọc private key không
    $resource = openssl_pkey_get_private($private_key);

    if ($resource === false) {
        $error = openssl_error_string();
        error_log('ChoAcc.com Sheets Error: OpenSSL không thể đọc private key: ' . $error);
        return false;
    }

    // Giải phóng resource
    if (is_resource($resource)) {
        openssl_free_key($resource);
    }

    return true;
}

/**
 * Hàm tự động sửa các lỗi private key phổ biến
 */
function choacc_sheets_auto_fix_common_private_key_issues($private_key) {
    // Danh sách các lỗi phổ biến và cách sửa
    $fixes = [
        // Lỗi 1: Thiếu dấu gạch ngang trong header/footer
        function($key) {
            $key = str_replace('BEGIN PRIVATE KEY', '-----BEGIN PRIVATE KEY-----', $key);
            $key = str_replace('END PRIVATE KEY', '-----END PRIVATE KEY-----', $key);
            return $key;
        },

        // Lỗi 2: Có khoảng trắng trong header/footer
        function($key) {
            $key = str_replace('---- BEGIN PRIVATE KEY ----', '-----BEGIN PRIVATE KEY-----', $key);
            $key = str_replace('---- END PRIVATE KEY ----', '-----END PRIVATE KEY-----', $key);
            return $key;
        },

        // Lỗi 3: Escape characters bị double-escape
        function($key) {
            $key = str_replace('\\\\n', "\n", $key);
            $key = str_replace('\\\\r', "\r", $key);
            $key = str_replace('\\\\t', "\t", $key);
            return $key;
        },

        // Lỗi 4: Newline bị thay thế bằng literal \n
        function($key) {
            $key = str_replace('\\n', "\n", $key);
            $key = str_replace('\\r', "\r", $key);
            return $key;
        },

        // Lỗi 5: Có ký tự Unicode hoặc BOM
        function($key) {
            // Loại bỏ BOM
            $key = str_replace("\xEF\xBB\xBF", '', $key);
            // Loại bỏ các ký tự không in được
            $key = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $key);
            return $key;
        },

        // Lỗi 6: Private key có format PKCS#8 nhưng bị hỏng
        function($key) {
            // Nếu có RSA PRIVATE KEY, chuyển về PRIVATE KEY
            $key = str_replace('-----BEGIN RSA PRIVATE KEY-----', '-----BEGIN PRIVATE KEY-----', $key);
            $key = str_replace('-----END RSA PRIVATE KEY-----', '-----END PRIVATE KEY-----', $key);
            return $key;
        }
    ];

    $fixed_key = $private_key;

    // Áp dụng tất cả các fix
    foreach ($fixes as $fix) {
        $fixed_key = $fix($fixed_key);
    }

    // Chuẩn hóa cuối cùng
    $fixed_key = trim($fixed_key);

    // Đảm bảo có đúng format newline và base64 encoding
    $lines = explode("\n", $fixed_key);
    $clean_lines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line)) {
            $clean_lines[] = $line;
        }
    }

    if (!empty($clean_lines)) {
        // Tái tạo private key với format chuẩn
        $header = '-----BEGIN PRIVATE KEY-----';
        $footer = '-----END PRIVATE KEY-----';
        $body_lines = [];

        $in_body = false;
        foreach ($clean_lines as $line) {
            if (strpos($line, '-----BEGIN') !== false) {
                $in_body = true;
                continue;
            }
            if (strpos($line, '-----END') !== false) {
                $in_body = false;
                continue;
            }
            if ($in_body) {
                $body_lines[] = $line;
            }
        }

        if (!empty($body_lines)) {
            // Ghép tất cả base64 content lại
            $base64_content = implode('', $body_lines);

            // Chia thành các dòng 64 ký tự (chuẩn PEM)
            $formatted_lines = [];
            for ($i = 0; $i < strlen($base64_content); $i += 64) {
                $formatted_lines[] = substr($base64_content, $i, 64);
            }

            $fixed_key = $header . "\n" . implode("\n", $formatted_lines) . "\n" . $footer;
        }
    }

    return $fixed_key;
}

/**
 * Hàm kiểm tra môi trường PHP và OpenSSL
 */
function choacc_sheets_check_php_environment() {
    $diagnosis = [];

    // Kiểm tra PHP version
    $php_version = phpversion();
    $diagnosis[] = "📋 **Thông tin môi trường PHP:**";
    $diagnosis[] = "➡️ PHP Version: " . $php_version;

    if (version_compare($php_version, '7.4', '<')) {
        $diagnosis[] = "⚠️ PHP version cũ (" . $php_version . "). Khuyến nghị nâng cấp lên PHP 7.4+ hoặc 8.0+";
    } else {
        $diagnosis[] = "✅ PHP version tốt (" . $php_version . ")";
    }

    // Kiểm tra OpenSSL extension
    if (extension_loaded('openssl')) {
        $diagnosis[] = "✅ OpenSSL extension đã được tải";

        // Kiểm tra OpenSSL version
        if (defined('OPENSSL_VERSION_TEXT')) {
            $diagnosis[] = "➡️ OpenSSL Version: " . OPENSSL_VERSION_TEXT;
        }

        // Kiểm tra các thuật toán OpenSSL
        $available_methods = openssl_get_md_methods();
        if (in_array('sha256', $available_methods)) {
            $diagnosis[] = "✅ SHA256 algorithm có sẵn";
        } else {
            $diagnosis[] = "❌ SHA256 algorithm không có sẵn";
        }

        // Kiểm tra cipher methods
        $cipher_methods = openssl_get_cipher_methods();
        $diagnosis[] = "➡️ Số lượng cipher methods: " . count($cipher_methods);

    } else {
        $diagnosis[] = "❌ OpenSSL extension KHÔNG được tải - Đây có thể là nguyên nhân chính!";
    }

    // Kiểm tra cURL extension
    if (extension_loaded('curl')) {
        $diagnosis[] = "✅ cURL extension đã được tải";

        $curl_version = curl_version();
        $diagnosis[] = "➡️ cURL Version: " . $curl_version['version'];
        $diagnosis[] = "➡️ SSL Version: " . $curl_version['ssl_version'];

        // Kiểm tra cURL có hỗ trợ SSL không
        if ($curl_version['features'] & CURL_VERSION_SSL) {
            $diagnosis[] = "✅ cURL hỗ trợ SSL";
        } else {
            $diagnosis[] = "❌ cURL KHÔNG hỗ trợ SSL";
        }

    } else {
        $diagnosis[] = "❌ cURL extension KHÔNG được tải";
    }

    // Kiểm tra JSON extension
    if (extension_loaded('json')) {
        $diagnosis[] = "✅ JSON extension đã được tải";
    } else {
        $diagnosis[] = "❌ JSON extension KHÔNG được tải";
    }

    // Kiểm tra memory limit
    $memory_limit = ini_get('memory_limit');
    $diagnosis[] = "➡️ Memory Limit: " . $memory_limit;

    // Kiểm tra max execution time
    $max_execution_time = ini_get('max_execution_time');
    $diagnosis[] = "➡️ Max Execution Time: " . $max_execution_time . " seconds";

    // Kiểm tra user agent
    $user_agent = ini_get('user_agent');
    $diagnosis[] = "➡️ User Agent: " . ($user_agent ?: 'Không đặt');

    // Kiểm tra allow_url_fopen
    $allow_url_fopen = ini_get('allow_url_fopen');
    $diagnosis[] = "➡️ allow_url_fopen: " . ($allow_url_fopen ? 'Bật' : 'Tắt');

    // Test tạo private key đơn giản
    $diagnosis[] = "";
    $diagnosis[] = "🔧 **Test OpenSSL cơ bản:**";

    try {
        // Tạo một private key test
        $test_key_resource = openssl_pkey_new([
            "digest_alg" => "sha256",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ]);

        if ($test_key_resource) {
            $diagnosis[] = "✅ OpenSSL có thể tạo private key mới";

            // Export private key
            if (openssl_pkey_export($test_key_resource, $test_private_key)) {
                $diagnosis[] = "✅ OpenSSL có thể export private key";

                // Test đọc lại private key
                $test_read = openssl_pkey_get_private($test_private_key);
                if ($test_read) {
                    $diagnosis[] = "✅ OpenSSL có thể đọc lại private key đã export";
                } else {
                    $diagnosis[] = "❌ OpenSSL KHÔNG thể đọc lại private key đã export";
                    $diagnosis[] = "➡️ OpenSSL Error: " . openssl_error_string();
                }
            } else {
                $diagnosis[] = "❌ OpenSSL KHÔNG thể export private key";
            }
        } else {
            $diagnosis[] = "❌ OpenSSL KHÔNG thể tạo private key mới";
            $diagnosis[] = "➡️ OpenSSL Error: " . openssl_error_string();
        }

    } catch (Exception $e) {
        $diagnosis[] = "❌ Lỗi khi test OpenSSL: " . $e->getMessage();
    }

    return $diagnosis;
}

/**
 * Hàm thử nhiều cách khác nhau để tạo Google Client
 */
function choacc_sheets_create_google_client_with_fallback($credentials) {
    $methods = [
        // Phương pháp 1: Sử dụng credentials đã được fix
        function($creds) {
            $client = new Google_Client();
            $client->setApplicationName('WooCommerce Google Sheets Integration');
            $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);
            $fixed_creds = choacc_sheets_fix_private_key($creds);
            $client->setAuthConfig($fixed_creds);
            return $client;
        },

        // Phương pháp 2: Tạo từ JSON string
        function($creds) {
            $client = new Google_Client();
            $client->setApplicationName('WooCommerce Google Sheets Integration');
            $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);
            $json_string = json_encode($creds);
            $client->setAuthConfig(json_decode($json_string, true));
            return $client;
        },

        // Phương pháp 3: Sử dụng putenv để set credentials
        function($creds) {
            $temp_file = tempnam(sys_get_temp_dir(), 'google_creds_');
            file_put_contents($temp_file, json_encode($creds));
            putenv('GOOGLE_APPLICATION_CREDENTIALS=' . $temp_file);

            $client = new Google_Client();
            $client->setApplicationName('WooCommerce Google Sheets Integration');
            $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);
            $client->useApplicationDefaultCredentials();

            // Cleanup
            unlink($temp_file);
            putenv('GOOGLE_APPLICATION_CREDENTIALS');

            return $client;
        }
    ];

    $last_error = '';

    foreach ($methods as $index => $method) {
        try {
            error_log('ChoAcc.com Sheets Debug: Trying Google Client creation method ' . ($index + 1));
            $client = $method($credentials);

            // Test client bằng cách thử lấy access token
            $token = $client->fetchAccessTokenWithAssertion();
            if (isset($token['access_token'])) {
                error_log('ChoAcc.com Sheets Success: Google Client created successfully with method ' . ($index + 1));
                return $client;
            }

        } catch (Exception $e) {
            $last_error = $e->getMessage();
            error_log('ChoAcc.com Sheets Debug: Method ' . ($index + 1) . ' failed: ' . $last_error);
            continue;
        }
    }

    error_log('ChoAcc.com Sheets Error: All Google Client creation methods failed. Last error: ' . $last_error);
    return null;
}

/**
 * Hàm lấy client Google Sheets API
 */
function choacc_sheets_get_sheets_service() {
    $config = choacc_sheets_get_config();
    $client = new Google_Client();
    $client->setApplicationName('WooCommerce Google Sheets Integration');
    $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);

    // Sử dụng thông tin xác thực tài khoản dịch vụ
    try {
        // Sử dụng hàm build credentials mới
        $credentials = choacc_sheets_build_credentials_from_fields($config);

        if (!$credentials) {
            error_log('ChoAcc.com Sheets Error: Không thể build credentials từ cấu hình');
            return null;
        }

        // Kiểm tra các trường bắt buộc trong credentials
        $required_fields = ['type', 'project_id', 'private_key', 'client_email'];
        foreach ($required_fields as $field) {
            if (!isset($credentials[$field]) || empty($credentials[$field])) {
                error_log('ChoAcc.com Sheets Error: Thiếu trường bắt buộc trong credentials: ' . $field);
                return null;
            }
        }

        // Log thông tin service account để debug (không log private key)
        error_log('ChoAcc.com Sheets Debug: Service Account Email: ' . $credentials['client_email']);
        error_log('ChoAcc.com Sheets Debug: Project ID: ' . $credentials['project_id']);

        // Xử lý private key để đảm bảo format đúng
        $credentials = choacc_sheets_fix_private_key($credentials);

        $client->setAuthConfig($credentials);

        // Cấu hình HttpClient với timeout và SSL/TLS
        $client_options = [
            'timeout' => 30,
            'connect_timeout' => 10,
        ];

        // Đường dẫn đến CA certificate bundle
        $ca_cert_path = CHOACC_SHEETS_PLUGIN_DIR . 'certs/cacert.pem';
        
        // Cấu hình xác minh SSL: Ưu tiên disable_ssl_verify, sau đó đến cacert.pem, cuối cùng là mặc định Guzzle (true)
        if ($config['disable_ssl_verify']) {
            $client_options['verify'] = false; // BUỘC TẮT hoàn toàn xác minh SSL
            error_log('ChoAcc.com Sheets WARNING: Xác minh SSL đã TẮT theo cấu hình. KHÔNG NÊN DÙNG TRONG MÔI TRƯỜNG THẬT!');
        } elseif (file_exists($ca_cert_path)) {
            $client_options['verify'] = $ca_cert_path;
            error_log('ChoAcc.com Sheets Debug: Sử dụng CA cert bundle từ: ' . $ca_cert_path);
        } else {
            // Mặc định Guzzle sẽ cố gắng sử dụng CA bundle của hệ thống nếu 'verify' không được đặt hoặc là true.
            // Nếu vẫn lỗi, có thể môi trường PHP/cURL không tìm thấy CA bundle của hệ thống.
            $client_options['verify'] = true; 
            error_log('ChoAcc.com Sheets Warning: cacert.pem không tìm thấy tại ' . $ca_cert_path . '. Xác minh SSL BẬT, dựa vào cấu hình hệ thống PHP.');
        }

        $client->setHttpClient(new \GuzzleHttp\Client($client_options));

    } catch (Exception $e) {
        error_log('ChoAcc.com Sheets Error: Lỗi xác thực Google Client: ' . $e->getMessage());
        return null;
    }

    return new Google_Service_Sheets($client);
}

/**
 * Hàm chẩn đoán chi tiết kết nối Google Sheets API
 */
function choacc_sheets_detailed_diagnosis() {
    $config = choacc_sheets_get_config();
    $diagnosis = [];

    // 1. Kiểm tra thư viện Google API Client
    if (!class_exists('Google_Client')) {
        $diagnosis[] = '❌ Thư viện Google API Client không được tải. Chạy composer install.';
        return $diagnosis;
    }
    $diagnosis[] = '✅ Thư viện Google API Client đã được tải.';

    // 2. Kiểm tra cấu hình cơ bản
    if (empty($config['spreadsheet_id'])) {
        $diagnosis[] = '❌ Google Sheet ID chưa được cấu hình.';
    } else {
        $diagnosis[] = '✅ Google Sheet ID: ' . esc_html(substr($config['spreadsheet_id'], 0, 10)) . '...';
    }

    if (empty($config['service_account_credentials_json'])) {
        $diagnosis[] = '❌ JSON Credentials chưa được cấu hình.';
        return $diagnosis;
    }

    // 3. Kiểm tra Service Account Credentials
    $credentials = choacc_sheets_build_credentials_from_fields($config);

    if (!$credentials) {
        $diagnosis[] = '❌ Không thể build Service Account credentials';
        $diagnosis[] = '💡 Vui lòng điền đầy đủ thông tin Service Account vào các trường riêng biệt.';
        $diagnosis[] = '💡 Hoặc sử dụng JSON credentials cũ nếu muốn.';
        return $diagnosis;
    }

    // Kiểm tra xem đang sử dụng phương pháp nào
    if (!empty($config['service_account_project_id'])) {
        $diagnosis[] = '✅ Đang sử dụng các trường Service Account riêng biệt (khuyến nghị).';
    } else {
        $diagnosis[] = '✅ Đang sử dụng JSON credentials (legacy mode).';
    }

    $diagnosis[] = '✅ Service Account Credentials hợp lệ.';

    // 4. Kiểm tra các trường bắt buộc
    $required_fields = ['type', 'project_id', 'private_key', 'client_email'];
    foreach ($required_fields as $field) {
        if (!isset($credentials[$field]) || empty($credentials[$field])) {
            $diagnosis[] = '❌ Thiếu trường bắt buộc: ' . $field;
        } else {
            if ($field === 'client_email') {
                $diagnosis[] = '✅ Service Account Email: ' . esc_html($credentials[$field]);
            } elseif ($field === 'project_id') {
                $diagnosis[] = '✅ Project ID: ' . esc_html($credentials[$field]);
            } elseif ($field === 'private_key') {
                // Kiểm tra chi tiết private key
                $private_key = $credentials[$field];
                $diagnosis[] = '✅ Trường private_key có sẵn.';

                // Kiểm tra format private key
                if (strpos($private_key, '-----BEGIN PRIVATE KEY-----') !== false) {
                    $diagnosis[] = '✅ Private key có header BEGIN đúng format.';
                } else {
                    $diagnosis[] = '❌ Private key thiếu header "-----BEGIN PRIVATE KEY-----".';
                }

                if (strpos($private_key, '-----END PRIVATE KEY-----') !== false) {
                    $diagnosis[] = '✅ Private key có footer END đúng format.';
                } else {
                    $diagnosis[] = '❌ Private key thiếu footer "-----END PRIVATE KEY-----".';
                }

                // Kiểm tra độ dài private key
                $key_length = strlen($private_key);
                if ($key_length > 1000) {
                    $diagnosis[] = '✅ Private key có độ dài hợp lý (' . $key_length . ' ký tự).';
                } else {
                    $diagnosis[] = '❌ Private key có vẻ quá ngắn (' . $key_length . ' ký tự). Có thể bị cắt cụt.';
                }
            } else {
                $diagnosis[] = '✅ Trường ' . esc_html($field) . ' có sẵn.';
            }
        }
    }

    // 4.1. Thử xử lý private key
    $diagnosis[] = '--- Kiểm tra xử lý Private Key ---';
    try {
        $fixed_credentials = choacc_sheets_fix_private_key($credentials);
        $diagnosis[] = '✅ Private key đã được xử lý và làm sạch thành công.';

        // Kiểm tra OpenSSL có thể đọc private key không
        if (choacc_sheets_test_private_key($fixed_credentials['private_key'])) {
            $diagnosis[] = '✅ OpenSSL có thể đọc và xác thực private key.';
        } else {
            $diagnosis[] = '❌ OpenSSL KHÔNG thể đọc private key. Đây chính là nguyên nhân lỗi "OpenSSL unable to validate key".';
            $diagnosis[] = '💡 Vui lòng tạo lại Service Account và tải xuống file JSON mới từ Google Cloud Console.';
        }

        // Thử tạo một Google Client để test private key
        $test_client = new Google_Client();
        $test_client->setAuthConfig($fixed_credentials);
        $diagnosis[] = '✅ Google Client có thể sử dụng private key đã xử lý.';

    } catch (Exception $e) {
        $diagnosis[] = '❌ Lỗi khi xử lý private key: ' . esc_html($e->getMessage());
        $diagnosis[] = '💡 Đây có thể là nguyên nhân gây ra lỗi "OpenSSL unable to validate key".';

        // Thêm thông tin chi tiết về lỗi
        if (strpos($e->getMessage(), 'OpenSSL') !== false) {
            $diagnosis[] = '💡 Lỗi OpenSSL: Private key có thể bị hỏng hoặc không đúng format.';
            $diagnosis[] = '💡 Giải pháp: Tạo lại Service Account trong Google Cloud Console và tải xuống file JSON mới.';
        }
    }
    $diagnosis[] = '--- Kết thúc kiểm tra Private Key ---';

    // 5. Kiểm tra sự tồn tại của cacert.pem
    $ca_cert_path = CHOACC_SHEETS_PLUGIN_DIR . 'certs/cacert.pem';
    if (file_exists($ca_cert_path)) {
        $diagnosis[] = '✅ Tệp cacert.pem được tìm thấy tại: ' . esc_html($ca_cert_path);
    } else {
        $diagnosis[] = '❌ Tệp cacert.pem KHÔNG tìm thấy tại: ' . esc_html($ca_cert_path) . '. Vui lòng tải xuống và đặt vào thư mục này.';
    }

    // 6. Kiểm tra cấu hình SSL/TLS của PHP
    $diagnosis[] = '--- Cấu hình SSL/TLS của PHP ---';
    $curl_cainfo = ini_get('curl.cainfo');
    $openssl_cafile = ini_get('openssl.cafile');
    $openssl_capath = ini_get('openssl.capath');

    $diagnosis[] = '➡️ curl.cainfo: ' . (empty($curl_cainfo) ? 'Không đặt' : esc_html($curl_cainfo));
    $diagnosis[] = '➡️ openssl.cafile: ' . (empty($openssl_cafile) ? 'Không đặt' : esc_html($openssl_cafile));
    $diagnosis[] = '➡️ openssl.capath: ' . (empty($openssl_capath) ? 'Không đặt' : esc_html($openssl_capath));

    // Kiểm tra xem các đường dẫn này có tồn tại không
    if (!empty($curl_cainfo) && !file_exists($curl_cainfo)) {
        $diagnosis[] = '❌ curl.cainfo chỉ đến tệp không tồn tại.';
    }
    if (!empty($openssl_cafile) && !file_exists($openssl_cafile)) {
        $diagnosis[] = '❌ openssl.cafile chỉ đến tệp không tồn tại.';
    }
    if (!empty($openssl_capath) && !is_dir($openssl_capath)) {
        $diagnosis[] = '❌ openssl.capath chỉ đến thư mục không tồn tại.';
    }
    $diagnosis[] = '--- Kết thúc cấu hình SSL/TLS của PHP ---';


    // 7. Thử tạo Google Client
    try {
        $client = new Google_Client();
        $client->setApplicationName('WooCommerce Google Sheets Integration');
        $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);

        // Sử dụng credentials đã được xử lý
        $fixed_credentials = choacc_sheets_fix_private_key($credentials);
        $client->setAuthConfig($fixed_credentials);
        
        // Cấu hình HttpClient với cacert.pem hoặc tắt xác minh cho mục đích chẩn đoán
        $client_options_diag = [
            'timeout' => 30,
            'connect_timeout' => 10,
        ];
        
        if ($config['disable_ssl_verify']) {
            $client_options_diag['verify'] = false; 
            $diagnosis[] = '⚠️ Xác minh SSL TẮT cho chẩn đoán theo cấu hình.';
        } elseif (file_exists($ca_cert_path)) {
            $client_options_diag['verify'] = $ca_cert_path;
            $diagnosis[] = '✅ Xác minh SSL BẬT, sử dụng cacert.pem từ plugin.';
        } else {
            // Nếu không tìm thấy cacert.pem và không tắt xác minh, Guzzle sẽ dựa vào cấu hình hệ thống/PHP
            $client_options_diag['verify'] = true; // Mặc định Guzzle
            $diagnosis[] = '⚠️ Xác minh SSL BẬT, dựa vào cấu hình hệ thống PHP (cacert.pem không tìm thấy trong plugin).';
        }
        $client->setHttpClient(new \GuzzleHttp\Client($client_options_diag));


        $diagnosis[] = '✅ Google Client được tạo thành công.';

        // 8. Thử tạo Sheets Service
        $service = new Google_Service_Sheets($client);
        $diagnosis[] = '✅ Google Sheets Service được tạo thành công.';

        // 9. Thử truy cập Google Sheet
        if (!empty($config['spreadsheet_id'])) {
            try {
                $spreadsheet = $service->spreadsheets->get($config['spreadsheet_id']);
                $diagnosis[] = '✅ Có thể truy cập Google Sheet: ' . esc_html($spreadsheet->getProperties()->getTitle());

                // 10. Liệt kê các tab
                $sheets = $spreadsheet->getSheets();
                $diagnosis[] = '📋 Các tab có sẵn:';
                foreach ($sheets as $sheet) {
                    $diagnosis[] = '   - ' . esc_html($sheet->getProperties()->getTitle());
                }

                // 11. Thử đọc dữ liệu từ tab đầu tiên
                if (!empty($sheets)) {
                    $firstSheetName = $sheets[0]->getProperties()->getTitle();
                    try {
                        $range = $firstSheetName . '!A1:G10'; // Đọc vài hàng đầu tiên để kiểm tra
                        $response = $service->spreadsheets_values->get($config['spreadsheet_id'], $range);
                        $values = $response->getValues();
                        if ($values) {
                            $diagnosis[] = '✅ Có thể đọc dữ liệu từ tab "' . esc_html($firstSheetName) . '" (' . count($values) . ' hàng).';
                            if (isset($values[0])) {
                                $diagnosis[] = '📄 Header (10 ký tự đầu): ' . esc_html(substr(implode(', ', $values[0]), 0, 100)) . '...';
                            }
                        } else {
                            $diagnosis[] = '⚠️ Tab "' . esc_html($firstSheetName) . '" không có dữ liệu.';
                        }
                    } catch (Exception $e) {
                        $diagnosis[] = '❌ Không thể đọc dữ liệu từ tab "' . esc_html($firstSheetName) . '": ' . esc_html($e->getMessage());
                    }
                }

            } catch (Google_Service_Exception $e) {
                $diagnosis[] = '❌ Lỗi Google API: ' . esc_html($e->getMessage());
                $diagnosis[] = '💡 Có thể do:';
                $diagnosis[] = '   - Service Account chưa được chia sẻ quyền truy cập vào Sheet';
                $diagnosis[] = '   - Google Sheet ID không chính xác';
                $diagnosis[] = '   - Google Sheets API chưa được kích hoạt trong Google Cloud Console';
                $diagnosis[] = '   - Lỗi SSL/TLS (nếu cacert.pem không đúng hoặc môi trường PHP có vấn đề)';
            } catch (Exception $e) {
                $diagnosis[] = '❌ Lỗi không xác định: ' . esc_html($e->getMessage());
            }
        }

    } catch (Exception $e) {
        $diagnosis[] = '❌ Không thể tạo Google Client: ' . esc_html($e->getMessage());
    }

    return $diagnosis;
}


/**
 * Hàm tìm và lấy một tài khoản cụ thể từ Google Sheet dựa trên ID và tên tab
 * @param string $sheet_name Tên tab (trang tính) trong Google Sheet
 * @param string $account_id_from_product ID tài khoản từ trường tùy chỉnh sản phẩm WooCommerce
 * @return array|null Mảng chứa thông tin tài khoản và số hàng, hoặc null nếu không tìm thấy
 */
function choacc_sheets_find_specific_account($sheet_name, $account_id_from_product) {
    $service = choacc_sheets_get_sheets_service();
    if ( ! $service ) {
        return null;
    }

    $config = choacc_sheets_get_config();
    $spreadsheetId = $config['spreadsheet_id'];
    $idColIndex = $config['columns']['ID']; // Lấy index của cột ID
    $statusColIndex = $config['columns']['Status']; // Lấy index của cột Status

    // Phạm vi đọc dữ liệu (ví dụ: A:G để đọc tất cả các cột liên quan)
    $range = $sheet_name . '!A:G'; // Điều chỉnh phạm vi nếu bạn có nhiều cột hơn

    try {
        $response = $service->spreadsheets_values->get($spreadsheetId, $range);
        $values = $response->getValues();

        if (empty($values)) {
            error_log('ChoAcc.com Sheets Info: Không có dữ liệu trong tab "' . $sheet_name . '" của Google Sheet.');
            return null;
        }

        // Bỏ qua hàng tiêu đề
        $header = array_shift($values); 

        $account_data = null;
        $row_index = 1; // Bắt đầu từ hàng 2 trong Sheet (index 1 trong mảng $values sau khi bỏ header)

        foreach ($values as $row) {
            $row_index++; // Tăng chỉ số hàng trong Google Sheet (để khớp với số hàng trong Sheet)
            // Đảm bảo cột ID và Status tồn tại trong hàng
            if (isset($row[$idColIndex]) && $row[$idColIndex] === $account_id_from_product &&
                isset($row[$statusColIndex]) && $row[$statusColIndex] === $config['status_available']) {
                
                // Lấy thông tin tài khoản dựa trên index cột đã định nghĩa
                $account_data = [
                    'username'  => isset($row[$config['columns']['Username']]) ? $row[$config['columns']['Username']] : '',
                    'password'  => isset($row[$config['columns']['Password']]) ? $row[$config['columns']['Password']] : '',
                    'login_url' => isset($row[$config['columns']['Login_URL']]) ? $row[$config['columns']['Login_URL']] : '',
                    'row_number' => $row_index, // Số hàng trong Google Sheet
                ];
                break; // Tìm thấy tài khoản phù hợp, thoát vòng lặp
            }
        }

        if ( ! $account_data ) {
            error_log('ChoAcc.com Sheets Info: Không tìm thấy tài khoản với ID "' . $account_id_from_product . '" hoặc tài khoản không "Available" trong tab "' . $sheet_name . '".');
        }

        return $account_data;

    } catch (Google_Service_Exception $e) {
        error_log('ChoAcc.com Sheets API Error (find_specific_account on tab ' . $sheet_name . '): ' . $e->getMessage());
        return null;
    } catch (Exception $e) {
        error_log('ChoAcc.com Sheets General Error (find_specific_account on tab ' . $sheet_name . '): ' . $e->getMessage());
        return null;
    }
}

/**
 * Hàm cập nhật trạng thái tài khoản trong Google Sheet
 * @param string $sheet_name Tên tab (trang tính) trong Google Sheet
 * @param int $row_number Số hàng cần cập nhật trong Google Sheet
 * @param string $order_id ID đơn hàng WooCommerce
 * @return bool True nếu cập nhật thành công, False nếu thất bại
 */
function choacc_sheets_update_account_status($sheet_name, $row_number, $order_id) {
    $service = choacc_sheets_get_sheets_service();
    if ( ! $service ) {
        return false;
    }

    $config = choacc_sheets_get_config();
    $spreadsheetId = $config['spreadsheet_id'];
    $statusColIndex = $config['columns']['Status'];
    $orderIdColIndex = $config['columns']['Order_ID'];
    $soldDateColIndex = $config['columns']['Sold_Date'];

    // Chuyển đổi index cột thành ký tự cột (A=0, B=1, C=2...)
    $statusColChar = chr(65 + $statusColIndex);
    $orderIdColChar = chr(65 + $orderIdColIndex);
    $soldDateColChar = chr(65 + $soldDateColIndex);

    // Phạm vi cập nhật (ví dụ: E2 cho cột Status ở hàng 2)
    $range_status = $sheet_name . '!' . $statusColChar . $row_number;
    $range_order_id = $sheet_name . '!' . $orderIdColChar . $row_number;
    $range_sold_date = $sheet_name . '!' . $soldDateColChar . $row_number;

    $values_status = [
        [$config['status_sold']]
    ];
    $values_order_id = [
        [$order_id]
    ];
    $values_sold_date = [
        [current_time('mysql')] // Sử dụng định dạng ngày giờ của WordPress
    ];

    $body_status = new Google_Service_Sheets_ValueRange(['values' => $values_status]);
    $body_order_id = new Google_Service_Sheets_ValueRange(['values' => $values_order_id]);
    $body_sold_date = new Google_Service_Sheets_ValueRange(['values' => $values_sold_date]);

    $params = ['valueInputOption' => 'RAW'];

    try {
        // Cập nhật trạng thái
        $service->spreadsheets_values->update($spreadsheetId, $range_status, $body_status, $params);
        // Cập nhật ID đơn hàng
        $service->spreadsheets_values->update($spreadsheetId, $range_order_id, $body_order_id, $params);
        // Cập nhật ngày bán
        $service->spreadsheets_values->update($spreadsheetId, $range_sold_date, $body_sold_date, $params);
        return true;
    } catch (Google_Service_Exception $e) {
        error_log('ChoAcc.com Sheets API Error (update_account_status on tab ' . $sheet_name . '): ' . $e->getMessage());
        return false;
    } catch (Exception $e) {
        error_log('ChoAcc.com Sheets General Error (update_account_status on tab ' . $sheet_name . '): ' . $e->getMessage());
        return false;
    }
}

/**
 * Template HTML cho email gửi khách hàng
 */
function choacc_sheets_get_email_template_html($data) {
    $config = choacc_sheets_get_config();
    
    $username = isset($data['username']) ? $data['username'] : 'Error';
    $password = isset($data['password']) ? $data['password'] : 'Error';
    $login_url = isset($data['login_url']) ? $data['login_url'] : $config['support_url']; // Nếu không có link đăng nhập, dùng link hỗ trợ
    $special_message = isset($data['special_message']) ? $data['special_message'] : '';
    $call_to_action = isset($data['call_to_action']) ? $data['call_to_action'] : 'Đăng nhập ngay';
    $button_text = isset($data['button_text']) ? $data['button_text'] : 'Đăng nhập ngay';

    $template = '
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thông tin Tài khoản của bạn - ' . esc_html($config['shop_name']) . '</title>
        <!-- Tailwind CSS CDN -->
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            /* Custom font for better aesthetics */
            body {
                font-family: \'Inter\', sans-serif;
                background-color: #f0f2f5; /* Light gray background */
            }
        </style>
    </head>
    <body class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white p-8 rounded-xl shadow-lg w-full max-w-2xl text-center">
            <div class="mb-8">
                <img src="' . esc_url($config['shop_logo_url']) . '" alt="' . esc_attr($config['shop_name']) . '" class="mx-auto rounded-md" style="max-width: 150px; height: auto;">
                <h1 class="text-4xl font-bold text-gray-800 mt-4">' . esc_html($config['shop_name']) . '</h1>
            </div>

            <h2 class="text-2xl font-semibold text-gray-700 mb-6">Chào mừng bạn đến với hệ thống của chúng tôi!</h2>
            <p class="text-gray-600 mb-6">
                Tài khoản bán hàng tự động của bạn đã được tạo thành công. Dưới đây là thông tin đăng nhập của bạn:
            </p>

            <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-800 p-6 rounded-lg mb-8 text-left">
                <p class="mb-2"><strong>Tên đăng nhập (Username):</strong> <span class="font-mono text-lg text-blue-700">' . esc_html($username) . '</span></p>
                <p><strong>Mật khẩu (Password):</strong> <span class="font-mono text-lg text-blue-700">' . esc_html($password) . '</span></p>
                <p class="mt-4 text-sm text-blue-700">
                    <em>' . esc_html($special_message) . '</em>
                </p>
            </div>

            <p class="text-gray-600 mb-8">
                Bạn có thể ' . esc_html($call_to_action) . ' bằng cách nhấp vào nút dưới đây:
            </p>

            <a href="' . esc_url($login_url) . '"
               class="inline-block py-3 px-8 bg-blue-600 hover:bg-blue-700 text-white text-xl font-bold rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                ' . esc_html($button_text) . '
            </a>

            <p class="text-gray-500 text-sm mt-10">
                Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với bộ phận hỗ trợ của chúng tôi tại
                <a href="' . esc_url($config['support_url']) . '" class="text-blue-600 hover:underline">' . esc_html($config['support_text']) . '</a>.
            </p>
            <p class="text-gray-500 text-sm mt-2">
                Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!
            </p>
            <p class="text-gray-500 text-sm mt-4">
                Trân trọng,<br>
                Đội ngũ ' . esc_html($config['shop_name']) . '
            </p>
        </div>
    </body>
    </html>';

    return $template;
}


/**
 * Hàm chính được kích hoạt khi đơn hàng WooCommerce hoàn thành
 * @param int $order_id ID của đơn hàng WooCommerce
 */
function choacc_sheets_distribute_account_on_order_complete($order_id) {
    // Đảm bảo WooCommerce đã được tải
    if ( ! class_exists( 'WooCommerce' ) ) {
        error_log('ChoAcc.com Sheets Error: WooCommerce không được kích hoạt.');
        return;
    }

    $order = wc_get_order( $order_id );
    $config = choacc_sheets_get_config();
    $product_account_id_meta_key = $config['product_account_id_meta_key'];
    $product_sheet_tab_meta_key = $config['product_sheet_tab_meta_key'];

    // Lấy ID tài khoản và tên tab từ sản phẩm trong đơn hàng
    $account_id_from_product = '';
    $sheet_tab_name_from_product = '';
    $product_in_order_name = ''; // Tên sản phẩm trong đơn hàng
    $customer_email = $order->get_billing_email();

    foreach ( $order->get_items() as $item_id => $item ) {
        $product = $item->get_product();
        if ( $product ) {
            $product_in_order_name = $product->get_name();
            // Lấy ID tài khoản và tên tab từ trường tùy chỉnh của sản phẩm
            $account_id_from_product = get_post_meta( $product->get_id(), $product_account_id_meta_key, true );
            $sheet_tab_name_from_product = get_post_meta( $product->get_id(), $product_sheet_tab_meta_key, true );
            
            // Chỉ xử lý nếu cả hai trường đều được điền
            if ( ! empty( $account_id_from_product ) && ! empty( $sheet_tab_name_from_product ) ) {
                break; // Tìm thấy sản phẩm tài khoản đầu tiên có đủ thông tin, thoát vòng lặp
            }
        }
    }

    $email_subject = sprintf( 'Thông tin tài khoản của bạn từ %s - Đơn hàng #%s', $config['shop_name'], $order_id );
    $email_data = [
        'username'        => 'Error',
        'password'        => 'Error',
        'login_url'       => $config['support_url'],
        'special_message' => 'Rất xin lỗi về sự cố đáng tiếc này!! Vui lòng liên hệ Fanpage để được hỗ trợ sớm nhất!! Chân Thành Xin Lỗi Quý Khách!!',
        'call_to_action'  => 'liên hệ Fanpage',
        'button_text'     => 'Liên hệ Fanpage',
    ];
    $admin_note_content = ''; // Ghi chú cho admin

    if ( empty( $account_id_from_product ) || empty( $sheet_tab_name_from_product ) ) {
        // Trường hợp không có sản phẩm nào trong đơn hàng có đủ thông tin cấu hình
        $admin_note_content = sprintf(
            'Lỗi: Đơn hàng #%s (%s) không chứa sản phẩm tài khoản có đủ ID Google Sheet và Tên Tab được cấu hình. Email lỗi đã được gửi đến khách hàng.',
            $order_id,
            $product_in_order_name
        );
        error_log('ChoAcc.com Sheets Info: ' . $admin_note_content);

    } else {
        // Lấy tài khoản cụ thể từ Google Sheet dựa trên TÊN TAB và ID sản phẩm
        $account_details = choacc_sheets_find_specific_account($sheet_tab_name_from_product, $account_id_from_product);

        if ( $account_details ) {
            // Cập nhật trạng thái tài khoản trong Google Sheet (trên đúng tab)
            $updated = choacc_sheets_update_account_status($sheet_tab_name_from_product, $account_details['row_number'], $order_id);

            if ( $updated ) {
                // Thành công: Chuẩn bị dữ liệu cho email
                $email_data['username'] = $account_details['username'];
                $email_data['password'] = $account_details['password'];
                $email_data['login_url'] = !empty($account_details['login_url']) ? $account_details['login_url'] : $config['support_url'];
                $email_data['special_message'] = 'Để bảo mật, chúng tôi khuyến khích bạn đổi mật khẩu ngay sau khi đăng nhập lần đầu.';
                $email_data['call_to_action'] = 'đăng nhập vào tài khoản của mình';
                $email_data['button_text'] = 'Đăng nhập ngay';

                $admin_note_content = sprintf(
                    'Thành công: Tài khoản đã được phân phối cho đơn hàng #%s (%s) (ID: %s, Tab: %s). Email đã được gửi đến khách hàng.',
                    $order_id,
                    $product_in_order_name,
                    $account_id_from_product,
                    $sheet_tab_name_from_product
                );
                error_log('ChoAcc.com Sheets Success: ' . $admin_note_content);

            } else {
                // Lỗi khi cập nhật trạng thái trong Google Sheet
                $admin_note_content = sprintf(
                    'Lỗi: Không thể cập nhật trạng thái tài khoản trong Google Sheet cho đơn hàng #%s (%s) (ID: %s, Tab: %s). Email lỗi đã được gửi đến khách hàng. Vui lòng kiểm tra log lỗi.',
                    $order_id,
                    $product_in_order_name,
                    $account_id_from_product,
                    $sheet_tab_name_from_product
                );
                error_log('ChoAcc.com Sheets Error: ' . $admin_note_content);
            }
        } else {
            // Không tìm thấy tài khoản có sẵn hoặc không khớp ID trên tab cụ thể
            $admin_note_content = sprintf(
                'Lỗi: Không tìm thấy tài khoản "Available" với ID "%s" trong tab "%s" để phân phối cho đơn hàng #%s (%s). Email lỗi đã được gửi đến khách hàng. Vui lòng kiểm tra Google Sheet của bạn và ID/Tên Tab sản phẩm.',
                $account_id_from_product,
                $sheet_tab_name_from_product,
                $order_id,
                $product_in_order_name
            );
            error_log('ChoAcc.com Sheets Error: ' . $admin_note_content);
        }
    }

    // Gửi email cho khách hàng
    $email_body = choacc_sheets_get_email_template_html($email_data);
    $headers = array('Content-Type: text/html; charset=UTF-8');

    $email_sent = wp_mail( $customer_email, $email_subject, $email_body, $headers );

    if ( ! $email_sent ) {
        $admin_note_content .= "\nLỗi: Không thể gửi email cho khách hàng. Vui lòng kiểm tra cấu hình email của WordPress.";
        error_log('ChoAcc.com Sheets Error: Không thể gửi email cho khách hàng ' . $customer_email . ' cho đơn hàng #' . $order_id);
    }

    // Thêm ghi chú vào đơn hàng (chỉ cho admin)
    $order->add_order_note( $admin_note_content, 0 ); // 0 = false, chỉ hiển thị cho admin
    $order->save(); // Lưu đơn hàng để ghi chú được thêm vào
}

// Hook vào hành động khi trạng thái đơn hàng WooCommerce thay đổi thành "completed"
add_action( 'woocommerce_order_status_completed', 'choacc_sheets_distribute_account_on_order_complete' );


/*
 * =============================================================================
 * ADMIN PAGE FUNCTIONS
 * =============================================================================
 */

// Thêm menu item vào Admin Dashboard
add_action( 'admin_menu', 'choacc_sheets_add_admin_menu' );
function choacc_sheets_add_admin_menu() {
    add_submenu_page(
        'woocommerce', // Parent slug
        'Quản lý Tài khoản Google Sheet', // Page title
        'Tài khoản Google Sheet', // Menu title
        'manage_options', // Capability required
        'choacc-sheets-integration', // Menu slug
        'choacc_sheets_admin_page_content' // Callback function to display the page content
    );
}

/**
 * Hiển thị nội dung trang quản trị
 */
function choacc_sheets_admin_page_content() {
    $config = choacc_sheets_get_config();
    $message = '';
    $message_type = ''; // 'success' or 'error'

    // Xử lý lưu cài đặt chung
    if ( isset( $_POST['choacc_sheets_save_settings'] ) && check_admin_referer( 'choacc_sheets_save_settings_nonce' ) ) {
        $new_settings = array(
            'spreadsheet_id'                   => sanitize_text_field( $_POST['spreadsheet_id'] ),
            // Các trường Service Account riêng biệt
            'service_account_type'             => sanitize_text_field( $_POST['service_account_type'] ?? 'service_account' ),
            'service_account_project_id'       => sanitize_text_field( $_POST['service_account_project_id'] ?? '' ),
            'service_account_private_key_id'   => sanitize_text_field( $_POST['service_account_private_key_id'] ?? '' ),
            'service_account_private_key'      => sanitize_textarea_field( $_POST['service_account_private_key'] ?? '' ),
            'service_account_client_email'     => sanitize_email( $_POST['service_account_client_email'] ?? '' ),
            'service_account_client_id'        => sanitize_text_field( $_POST['service_account_client_id'] ?? '' ),
            'service_account_auth_uri'         => esc_url_raw( $_POST['service_account_auth_uri'] ?? 'https://accounts.google.com/o/oauth2/auth' ),
            'service_account_token_uri'        => esc_url_raw( $_POST['service_account_token_uri'] ?? 'https://oauth2.googleapis.com/token' ),
            'service_account_auth_provider_x509_cert_url' => esc_url_raw( $_POST['service_account_auth_provider_x509_cert_url'] ?? 'https://www.googleapis.com/oauth2/v1/certs' ),
            'service_account_client_x509_cert_url' => esc_url_raw( $_POST['service_account_client_x509_cert_url'] ?? '' ),
            // Giữ lại JSON credentials để backward compatibility
            'service_account_credentials_json' => stripslashes( sanitize_textarea_field( $_POST['service_account_credentials_json'] ?? '' ) ),
            'status_available'                 => sanitize_text_field( $_POST['status_available'] ),
            'status_sold'                      => sanitize_text_field( $_POST['status_sold'] ),
            'shop_name'                        => sanitize_text_field( $_POST['shop_name'] ),
            'shop_logo_url'                    => esc_url_raw( $_POST['shop_logo_url'] ),
            'support_url'                      => esc_url_raw( $_POST['support_url'] ),
            'support_text'                     => sanitize_text_field( $_POST['support_text'] ),
            'disable_ssl_verify'               => isset( $_POST['disable_ssl_verify'] ) ? (bool) $_POST['disable_ssl_verify'] : false,
        );

        // Xử lý cột
        $columns = [];
        foreach ( $_POST['columns'] as $key => $value ) {
            $columns[$key] = absint( $value ); // Đảm bảo là số nguyên dương
        }
        $new_settings['columns'] = $columns;

        update_option( 'choacc_sheets_settings', $new_settings );
        $message = 'Cài đặt đã được lưu thành công!';
        $message_type = 'success';
    }

    // Xử lý lưu liên kết sản phẩm
    if ( isset( $_POST['choacc_sheets_save_product_links'] ) && check_admin_referer( 'choacc_sheets_save_product_links_nonce' ) ) {
        $product_ids = isset( $_POST['product_ids'] ) ? array_map( 'absint', $_POST['product_ids'] ) : [];
        $account_ids = isset( $_POST['account_id'] ) ? array_map( 'sanitize_text_field', $_POST['account_id'] ) : [];
        $tab_names = isset( $_POST['tab_name'] ) ? array_map( 'sanitize_text_field', $_POST['tab_name'] ) : [];

        foreach ( $product_ids as $product_id ) {
            $current_account_id = isset( $account_ids[ $product_id ] ) ? $account_ids[ $product_id ] : '';
            $current_tab_name = isset( $tab_names[ $product_id ] ) ? $tab_names[ $product_id ] : '';

            update_post_meta( $product_id, $config['product_account_id_meta_key'], $current_account_id );
            update_post_meta( $product_id, $config['product_sheet_tab_meta_key'], $current_tab_name );
        }
        $message = 'Liên kết sản phẩm đã được lưu thành công!';
        $message_type = 'success';
    }

    // Xử lý kiểm tra môi trường PHP
    if ( isset( $_POST['choacc_sheets_check_php_env'] ) && check_admin_referer( 'choacc_sheets_check_php_env_nonce' ) ) {
        $php_diagnosis = choacc_sheets_check_php_environment();
        $message = '<strong>Kết quả kiểm tra môi trường PHP:</strong><br><br>' . implode('<br>', $php_diagnosis);
        $message_type = 'info';
    }

    // Xử lý kiểm tra kết nối API
    if ( isset( $_POST['choacc_sheets_test_connection'] ) && check_admin_referer( 'choacc_sheets_test_connection_nonce' ) ) {
        $service = choacc_sheets_get_sheets_service();
        if ( $service ) {
            try {
                $spreadsheetId = $config['spreadsheet_id'];
                // Thử đọc một ô bất kỳ để kiểm tra kết nối
                // Lấy tên tab đầu tiên trong Sheet nếu có, hoặc dùng một tên mặc định
                $first_sheet_name = 'Sheet1'; // Mặc định
                try {
                    $spreadsheet_properties = $service->spreadsheets->get($spreadsheetId)->getSheets();
                    if (!empty($spreadsheet_properties)) {
                        $first_sheet_name = $spreadsheet_properties[0]->getProperties()->getTitle();
                    }
                } catch (Exception $e) {
                    // Bỏ qua lỗi nếu không thể lấy tên sheet, dùng mặc định
                }

                $range = $first_sheet_name . '!A1';
                $response = $service->spreadsheets_values->get($spreadsheetId, $range);
                $message = 'Kết nối Google Sheets API thành công! Có thể đọc dữ liệu từ Sheet.';
                $message_type = 'success';
            } catch (Google_Service_Exception $e) {
                $message = 'Lỗi kết nối Google Sheets API: ' . $e->getMessage();
                $message_type = 'error';
            } catch (Exception $e) {
                $message = 'Lỗi không xác định khi kiểm tra kết nối: ' . $e->getMessage();
                $message_type = 'error';
            }
        } else {
            $message = 'Không thể khởi tạo dịch vụ Google Sheets. Vui lòng kiểm tra JSON Credentials và Sheet ID.';
            $message_type = 'error';
        }
    }

    // Xử lý chẩn đoán chi tiết
    if ( isset( $_POST['choacc_sheets_detailed_diagnosis'] ) && check_admin_referer( 'choacc_sheets_detailed_diagnosis_nonce' ) ) {
        $diagnosis_results = choacc_sheets_detailed_diagnosis();
        $message = '<strong>Kết quả chẩn đoán chi tiết:</strong><br><br>' . implode('<br>', $diagnosis_results);
        $message_type = 'info';
    }

    // Xử lý kiểm tra liên kết sản phẩm cụ thể (qua AJAX)
    if ( isset( $_POST['action'] ) && $_POST['action'] === 'choacc_sheets_test_product_link_ajax' ) {
        check_ajax_referer( 'choacc_sheets_test_product_link_nonce', 'security' );
        $product_id = absint( $_POST['product_id'] );
        $account_id = sanitize_text_field( $_POST['account_id'] );
        $tab_name = sanitize_text_field( $_POST['tab_name'] );

        if ( empty( $account_id ) || empty( $tab_name ) ) {
            wp_send_json_error( 'Vui lòng điền đầy đủ ID tài khoản và Tên tab.' );
        }

        $account_details = choacc_sheets_find_specific_account( $tab_name, $account_id );
        if ( $account_details ) {
            wp_send_json_success( 'Tìm thấy tài khoản: ' . esc_html($account_details['username']) . ' (Hàng ' . esc_html($account_details['row_number']) . '). Trạng thái: ' . esc_html($config['status_available']) );
        } else {
            wp_send_json_error( 'Không tìm thấy tài khoản "Available" với ID "' . esc_html($account_id) . '" trong tab "' . esc_html($tab_name) . '". Vui lòng kiểm tra lại.' );
        }
        wp_die();
    }

    // Lấy cài đặt hiện tại để hiển thị trong form
    $current_settings = choacc_sheets_get_config();

    // Lấy danh sách sản phẩm WooCommerce
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => -1, // Lấy tất cả sản phẩm
        'orderby'        => 'title',
        'order'          => 'ASC',
        'fields'         => 'ids', // Chỉ lấy ID để tối ưu
    );
    $product_ids = get_posts( $args );
    ?>
    <div class="wrap">
        <h1>Quản lý Tài khoản Google Sheet cho ChoAcc.com</h1>

        <?php if ( $message ) : ?>
            <div class="notice notice-<?php echo esc_attr( $message_type ); ?> is-dismissible">
                <p><?php
                    if ( $message_type === 'info' ) {
                        echo wp_kses_post( $message ); // Cho phép HTML cho thông báo chẩn đoán
                    } else {
                        echo esc_html( $message );
                    }
                ?></p>
            </div>
        <?php endif; ?>

        <!-- Công cụ chuyển đổi JSON tự động -->
        <div class="notice notice-success">
            <h3>🚀 Công cụ chuyển đổi JSON tự động (Khuyến nghị)</h3>
            <p><strong>Paste toàn bộ file JSON vào đây để tự động điền các trường:</strong></p>
            <textarea id="json_converter" rows="8" cols="80" class="large-text code" placeholder="Paste toàn bộ nội dung file JSON của bạn vào đây..."></textarea>
            <br><br>
            <button type="button" id="convert_json" class="button button-primary">🔄 Chuyển đổi và điền tự động</button>
            <button type="button" id="clear_fields" class="button button-secondary">🗑️ Xóa tất cả trường</button>
            <p class="description"><strong>Cách sử dụng:</strong> Copy toàn bộ nội dung file JSON → Paste vào ô trên → Nhấn "Chuyển đổi" → Các trường sẽ được điền tự động</p>
        </div>

        <!-- Hướng dẫn khắc phục lỗi OpenSSL -->
        <div class="notice notice-info">
            <h3>🔧 Khắc phục lỗi "OpenSSL unable to validate key"</h3>
            <p><strong>Nếu bạn gặp lỗi này, hãy thực hiện các bước sau:</strong></p>
            <ol>
                <li><strong>Sử dụng công cụ chuyển đổi JSON ở trên</strong> (cách dễ nhất)</li>
                <li><strong>Hoặc nhập từng trường riêng biệt</strong> theo hướng dẫn bên dưới</li>
                <li><strong>Chạy chẩn đoán chi tiết:</strong> Sử dụng nút "🔍 Chẩn đoán Chi tiết" để kiểm tra</li>
                <li><strong>Nếu vẫn lỗi:</strong> Tạo lại Service Account từ Google Cloud Console</li>
            </ol>
            <p><em>Lưu ý: Công cụ chuyển đổi tự động sẽ xử lý đúng format private key và tránh lỗi escape characters.</em></p>
        </div>

        <h2 class="title">Cài đặt Chung</h2>
        <form method="post" action="">
            <?php wp_nonce_field( 'choacc_sheets_save_settings_nonce' ); ?>
            <table class="form-table">
                <tbody>
                    <tr>
                        <th scope="row"><label for="spreadsheet_id">Google Sheet ID</label></th>
                        <td>
                            <input name="spreadsheet_id" type="text" id="spreadsheet_id" value="<?php echo esc_attr( $current_settings['spreadsheet_id'] ); ?>" class="regular-text" placeholder="ID của Google Sheet của bạn">
                            <p class="description">Bạn có thể tìm thấy ID này trong URL của Google Sheet (ví dụ: <code>https://docs.google.com/spreadsheets/d/<span style="color:red;">YOUR_SHEET_ID</span>/edit</code>)</p>
                        </td>
                    </tr>
                    <!-- Service Account Fields - Phương pháp mới (khuyến nghị) -->
                    <tr>
                        <th scope="row" colspan="2">
                            <h3 style="margin: 0; color: #0073aa;">🔧 Service Account Credentials (Khuyến nghị - Nhập từng trường riêng biệt)</h3>
                            <p style="font-weight: normal; color: #666;">Nhập từng trường riêng biệt từ file JSON để tránh lỗi format</p>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_project_id">Project ID</label></th>
                        <td>
                            <input name="service_account_project_id" type="text" id="service_account_project_id" value="<?php echo esc_attr( $current_settings['service_account_project_id'] ); ?>" class="regular-text" placeholder="ví dụ: my-project-123456">
                            <p class="description">Tìm trong JSON: <code>"project_id": "..."</code></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_private_key_id">Private Key ID</label></th>
                        <td>
                            <input name="service_account_private_key_id" type="text" id="service_account_private_key_id" value="<?php echo esc_attr( $current_settings['service_account_private_key_id'] ); ?>" class="regular-text" placeholder="ví dụ: abc123def456...">
                            <p class="description">Tìm trong JSON: <code>"private_key_id": "..."</code></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_private_key">Private Key</label></th>
                        <td>
                            <textarea name="service_account_private_key" id="service_account_private_key" rows="8" cols="70" class="large-text code" placeholder="-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
-----END PRIVATE KEY-----"><?php echo esc_textarea( $current_settings['service_account_private_key'] ); ?></textarea>
                            <p class="description">Tìm trong JSON: <code>"private_key": "..."</code> - Copy toàn bộ private key bao gồm cả header và footer</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_client_email">Client Email</label></th>
                        <td>
                            <input name="service_account_client_email" type="email" id="service_account_client_email" value="<?php echo esc_attr( $current_settings['service_account_client_email'] ); ?>" class="regular-text" placeholder="ví dụ: <EMAIL>">
                            <p class="description">Tìm trong JSON: <code>"client_email": "..."</code></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_client_id">Client ID</label></th>
                        <td>
                            <input name="service_account_client_id" type="text" id="service_account_client_id" value="<?php echo esc_attr( $current_settings['service_account_client_id'] ); ?>" class="regular-text" placeholder="ví dụ: 123456789012345678901">
                            <p class="description">Tìm trong JSON: <code>"client_id": "..."</code></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_client_x509_cert_url">Client X509 Cert URL</label></th>
                        <td>
                            <input name="service_account_client_x509_cert_url" type="url" id="service_account_client_x509_cert_url" value="<?php echo esc_attr( $current_settings['service_account_client_x509_cert_url'] ); ?>" class="regular-text" placeholder="https://www.googleapis.com/robot/v1/metadata/x509/...">
                            <p class="description">Tìm trong JSON: <code>"client_x509_cert_url": "..."</code></p>
                        </td>
                    </tr>

                    <!-- Legacy JSON Method -->
                    <tr>
                        <th scope="row" colspan="2">
                            <h3 style="margin: 20px 0 0 0; color: #d63638;">⚠️ Phương pháp cũ (Legacy) - Chỉ dùng nếu cần thiết</h3>
                            <p style="font-weight: normal; color: #666;">Nếu bạn muốn sử dụng JSON trực tiếp (dễ bị lỗi format)</p>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_credentials_json">JSON Service Account Credentials</label></th>
                        <td>
                            <textarea name="service_account_credentials_json" id="service_account_credentials_json" rows="6" cols="70" class="large-text code" placeholder="Dán nội dung JSON của tài khoản dịch vụ vào đây (không khuyến nghị)"><?php echo esc_textarea( $current_settings['service_account_credentials_json'] ); ?></textarea>
                            <p class="description" style="color: #d63638;"><strong>Lưu ý:</strong> Phương pháp này dễ gây lỗi "OpenSSL unable to validate key". Khuyến nghị sử dụng các trường riêng biệt ở trên.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Chỉ số cột trong Google Sheet (bắt đầu từ 0 cho cột A)</th>
                        <td>
                            <?php foreach ( $current_settings['columns'] as $col_name => $col_index ) : ?>
                                <p>
                                    <label for="column_<?php echo esc_attr( $col_name ); ?>"><?php echo esc_html( $col_name ); ?>:</label>
                                    <input name="columns[<?php echo esc_attr( $col_name ); ?>]" type="number" id="column_<?php echo esc_attr( $col_name ); ?>" value="<?php echo esc_attr( $col_index ); ?>" class="small-text">
                                </p>
                            <?php endforeach; ?>
                            <p class="description">Đảm bảo các chỉ số này khớp với vị trí cột trong Google Sheet của bạn (A=0, B=1, C=2...).</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="status_available">Trạng thái "Còn hàng"</label></th>
                        <td>
                            <input name="status_available" type="text" id="status_available" value="<?php echo esc_attr( $current_settings['status_available'] ); ?>" class="regular-text">
                            <p class="description">Giá trị trong cột Trạng thái của Google Sheet khi tài khoản còn hàng.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="status_sold">Trạng thái "Đã bán"</label></th>
                        <td>
                            <input name="status_sold" type="text" id="status_sold" value="<?php echo esc_attr( $current_settings['status_sold'] ); ?>" class="regular-text">
                            <p class="description">Giá trị trong cột Trạng thái của Google Sheet khi tài khoản đã bán.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="shop_name">Tên cửa hàng</label></th>
                        <td>
                            <input name="shop_name" type="text" id="shop_name" value="<?php echo esc_attr( $current_settings['shop_name'] ); ?>" class="regular-text">
                            <p class="description">Tên cửa hàng sẽ hiển thị trong email gửi khách hàng.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="shop_logo_url">URL Logo cửa hàng</label></th>
                        <td>
                            <input name="shop_logo_url" type="url" id="shop_logo_url" value="<?php echo esc_attr( $current_settings['shop_logo_url'] ); ?>" class="regular-text" placeholder="http://yourdomain.com/your-logo.png">
                            <p class="description">URL đầy đủ của logo cửa hàng (sẽ hiển thị trong email).</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="support_url">URL Hỗ trợ / Fanpage</label></th>
                        <td>
                            <input name="support_url" type="url" id="support_url" value="<?php echo esc_attr( $current_settings['support_url'] ); ?>" class="regular-text" placeholder="https://www.facebook.com/yourfanpage">
                            <p class="description">URL liên hệ hỗ trợ hoặc Fanpage của bạn.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="support_text">Văn bản hỗ trợ</label></th>
                        <td>
                            <input name="support_text" type="text" id="support_text" value="<?php echo esc_attr( $current_settings['support_text'] ); ?>" class="regular-text" placeholder="Fanpage của chúng tôi">
                            <p class="description">Văn bản hiển thị cho liên kết hỗ trợ trong email.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Tắt xác minh SSL (Chỉ gỡ lỗi)</th>
                        <td>
                            <input name="disable_ssl_verify" type="checkbox" id="disable_ssl_verify" value="1" <?php checked( $current_settings['disable_ssl_verify'], true ); ?>>
                            <p class="description" style="color: red; font-weight: bold;">
                                CẢNH BÁO: Tắt xác minh SSL là một rủi ro bảo mật nghiêm trọng và KHÔNG NÊN DÙNG TRONG MÔI TRƯỜNG SẢN PHẨM. Chỉ sử dụng để gỡ lỗi khi bạn chắc chắn vấn đề là SSL.
                            </p>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p class="submit">
                <input type="submit" name="choacc_sheets_save_settings" id="submit" class="button button-primary" value="Lưu Cài đặt">
                <input type="submit" name="choacc_sheets_test_connection" id="test_connection" class="button button-secondary" value="Kiểm tra Kết nối API">
                <input type="submit" name="choacc_sheets_check_php_env" id="check_php_env" class="button button-secondary" value="🔍 Kiểm tra Môi trường PHP">
            </p>
        </form>

        <!-- Form riêng cho chẩn đoán chi tiết -->
        <form method="post" action="" style="display: inline;">
            <?php wp_nonce_field( 'choacc_sheets_detailed_diagnosis_nonce' ); ?>
            <p class="submit" style="margin-top: 0;">
                <input type="submit" name="choacc_sheets_detailed_diagnosis" id="detailed_diagnosis" class="button button-secondary" value="🔍 Chẩn đoán Chi tiết" style="background-color: #0073aa; color: white;">
            </p>
        </form>

        <hr>

        <h2 class="title">Quản lý Liên kết Tài khoản Sản phẩm</h2>
        <p class="description">Nhập ID tài khoản và Tên tab Google Sheet cho từng sản phẩm WooCommerce. ID tài khoản phải khớp với cột "ID" trong tab đó.</p>

        <form method="post" action="">
            <?php wp_nonce_field( 'choacc_sheets_save_product_links_nonce' ); ?>
            <table class="wp-list-table widefat fixed striped products">
                <thead>
                    <tr>
                        <th scope="col">Sản phẩm (ID)</th>
                        <th scope="col">SKU</th>
                        <th scope="col">Google Sheet Account ID</th>
                        <th scope="col">Google Sheet Tab Name</th>
                        <th scope="col">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( ! empty( $product_ids ) ) : ?>
                        <?php foreach ( $product_ids as $product_id ) :
                            $product = wc_get_product( $product_id );
                            if ( ! $product ) continue;

                            $current_account_id = get_post_meta( $product_id, $config['product_account_id_meta_key'], true );
                            $current_tab_name = get_post_meta( $product_id, $config['product_sheet_tab_meta_key'], true );
                            ?>
                            <tr>
                                <td>
                                    <input type="hidden" name="product_ids[]" value="<?php echo esc_attr( $product_id ); ?>">
                                    <a href="<?php echo esc_url( get_edit_post_link( $product_id ) ); ?>" target="_blank">
                                        <?php echo esc_html( $product->get_name() ); ?> (#<?php echo esc_html( $product_id ); ?>)
                                    </a>
                                </td>
                                <td><?php echo esc_html( $product->get_sku() ); ?></td>
                                <td>
                                    <input type="text" name="account_id[<?php echo esc_attr( $product_id ); ?>]" 
                                           value="<?php echo esc_attr( $current_account_id ); ?>" 
                                           class="choacc-sheets-account-id regular-text" placeholder="ID tài khoản">
                                </td>
                                <td>
                                    <input type="text" name="tab_name[<?php echo esc_attr( $product_id ); ?>]" 
                                           value="<?php echo esc_attr( $current_tab_name ); ?>" 
                                           class="choacc-sheets-tab-name regular-text" placeholder="Tên tab">
                                </td>
                                <td>
                                    <button type="button" class="button button-small choacc-sheets-test-link" 
                                            data-product-id="<?php echo esc_attr( $product_id ); ?>">
                                        Test
                                    </button>
                                    <span class="choacc-sheets-test-result" id="test-result-<?php echo esc_attr( $product_id ); ?>"></span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="5">Không tìm thấy sản phẩm WooCommerce nào.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            <p class="submit">
                <input type="submit" name="choacc_sheets_save_product_links" id="submit_product_links" class="button button-primary" value="Lưu Liên kết Sản phẩm">
            </p>
        </form>
    </div>

    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.choacc-sheets-test-link').on('click', function() {
                var button = $(this);
                var productId = button.data('product-id');
                var accountId = button.closest('tr').find('.choacc-sheets-account-id').val();
                var tabName = button.closest('tr').find('.choacc-sheets-tab-name').val();
                var resultSpan = $('#test-result-' + productId);

                resultSpan.html('<span style="color: gray;">Đang kiểm tra...</span>');
                button.prop('disabled', true); // Vô hiệu hóa nút trong khi kiểm tra

                $.ajax({
                    url: ajaxurl, // ajaxurl được WordPress định nghĩa sẵn
                    type: 'POST',
                    data: {
                        action: 'choacc_sheets_test_product_link_ajax',
                        security: '<?php echo wp_create_nonce( 'choacc_sheets_test_product_link_nonce' ); ?>',
                        product_id: productId,
                        account_id: accountId,
                        tab_name: tabName
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color: green;">' + response.data + '</span>');
                        } else {
                            resultSpan.html('<span style="color: red;">' + response.data + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color: red;">Lỗi không xác định khi kiểm tra.</span>');
                    },
                    complete: function() {
                        button.prop('disabled', false); // Kích hoạt lại nút
                    }
                });
            });
        });

        // Xử lý chuyển đổi JSON tự động
        $('#convert_json').click(function() {
            var jsonText = $('#json_converter').val().trim();

            if (!jsonText) {
                alert('Vui lòng paste nội dung JSON vào ô trên!');
                return;
            }

            try {
                var jsonData = JSON.parse(jsonText);

                // Điền các trường từ JSON
                if (jsonData.project_id) {
                    $('#service_account_project_id').val(jsonData.project_id);
                }

                if (jsonData.private_key_id) {
                    $('#service_account_private_key_id').val(jsonData.private_key_id);
                }

                if (jsonData.private_key) {
                    // Xử lý private key: chuyển \n thành xuống dòng thật
                    var privateKey = jsonData.private_key.replace(/\\n/g, '\n');
                    $('#service_account_private_key').val(privateKey);
                }

                if (jsonData.client_email) {
                    $('#service_account_client_email').val(jsonData.client_email);
                }

                if (jsonData.client_id) {
                    $('#service_account_client_id').val(jsonData.client_id);
                }

                if (jsonData.client_x509_cert_url) {
                    $('#service_account_client_x509_cert_url').val(jsonData.client_x509_cert_url);
                }

                // Xóa JSON converter sau khi chuyển đổi thành công
                $('#json_converter').val('');

                alert('✅ Chuyển đổi thành công! Các trường đã được điền tự động. Hãy nhấn "Lưu Cài đặt" để lưu.');

                // Scroll xuống để user thấy các trường đã được điền
                $('html, body').animate({
                    scrollTop: $('#service_account_project_id').offset().top - 100
                }, 500);

            } catch (e) {
                alert('❌ Lỗi: JSON không hợp lệ. Vui lòng kiểm tra lại nội dung JSON.\n\nLỗi chi tiết: ' + e.message);
            }
        });

        // Xử lý xóa tất cả trường
        $('#clear_fields').click(function() {
            if (confirm('Bạn có chắc muốn xóa tất cả các trường Service Account?')) {
                $('#service_account_project_id').val('');
                $('#service_account_private_key_id').val('');
                $('#service_account_private_key').val('');
                $('#service_account_client_email').val('');
                $('#service_account_client_id').val('');
                $('#service_account_client_x509_cert_url').val('');
                $('#json_converter').val('');
                alert('✅ Đã xóa tất cả các trường!');
            }
        });
    </script>
    <?php
}

// Xử lý AJAX cho nút test liên kết sản phẩm cụ thể
add_action( 'wp_ajax_choacc_sheets_test_product_link_ajax', 'choacc_sheets_admin_page_content' );

