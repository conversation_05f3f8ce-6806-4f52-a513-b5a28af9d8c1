<?php
/**
 * Plugin Name: ChoAcc.com - Google Sheets Integration
 * Plugin URI:  https://choacc.com/
 * Description: Tự động phân phối tài khoản từ Google Sheets qua email khi đơn hàng WooCommerce hoàn thành, với trang quản lý tập trung.
 * Version:     1.3.6
 * Author:      ChoAcc.com
 * Author URI:  https://yourwebsite.com
 * License:     GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: choacc-sheets
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Định nghĩa đường dẫn đến thư mục vendor (nơi chứa Google API Client Library)
define( 'CHOACC_SHEETS_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'CHOACC_SHEETS_VENDOR_DIR', CHOACC_SHEETS_PLUGIN_DIR . 'vendor/' );

// <PERSON><PERSON>m tra xem thư viện Google API Client đã được tải chưa
if ( file_exists( CHOACC_SHEETS_VENDOR_DIR . 'autoload.php' ) ) {
    require_once CHOACC_SHEETS_VENDOR_DIR . 'autoload.php';
} else {
    // Thông báo lỗi nếu thư viện không tìm thấy (quan trọng!)
    add_action( 'admin_notices', function() {
        echo '<div class="notice notice-error"><p><strong>ChoAcc.com Google Sheets Integration:</strong> Thư viện Google API Client không tìm thấy. Vui lòng chạy `composer install` trong thư mục plugin hoặc tải thư mục `vendor` lên.</p></div>';
    });
    return; // Dừng kích hoạt plugin nếu không có thư viện
}

/**
 * Cấu hình plugin: Lấy từ tùy chọn WordPress hoặc giá trị mặc định
 */
function choacc_sheets_get_config() {
    $default_config = array(
        'spreadsheet_id'                   => '',
        'service_account_credentials_json' => '',
        'columns'                          => [
            'ID'         => 0, // Cột A (index 0)
            'Username'   => 1, // Cột B (index 1)
            'Password'   => 2, // Cột C (index 2)
            'Login_URL'  => 3, // Cột D (index 3)
            'Status'     => 4, // Cột E (index 4)
            'Order_ID'   => 5, // Cột F (index 5)
            'Sold_Date'  => 6, // Cột G (index 6)
        ],
        'status_available'                 => 'Available',
        'status_sold'                      => 'Sold',
        'product_account_id_meta_key'      => '_choacc_sheets_account_id',
        'product_sheet_tab_meta_key'       => '_choacc_sheets_tab_name',
        'shop_name'                        => 'ChoAcc.com',
        'shop_logo_url'                    => 'http://choacc.com/wp-content/uploads/2025/07/Tosca-Orange-Modern-Online-Shop-Logo-3.png',
        'support_url'                      => 'https://www.facebook.com/taikhoansieurecom',
        'support_text'                     => 'Fanpage của chúng tôi',
        'disable_ssl_verify'               => false, // Mặc định là FALSE (không tắt xác minh SSL)
    );

    $saved_settings = get_option( 'choacc_sheets_settings', array() );
    return array_merge( $default_config, $saved_settings );
}

/**
 * Hàm lấy client Google Sheets API
 */
function choacc_sheets_get_sheets_service() {
    $config = choacc_sheets_get_config();
    $client = new Google_Client();
    $client->setApplicationName('WooCommerce Google Sheets Integration');
    $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);

    // Sử dụng thông tin xác thực tài khoản dịch vụ
    try {
        $raw_json = $config['service_account_credentials_json'];
        // Log chuỗi JSON thô trước khi xử lý
        error_log('ChoAcc.com Sheets Debug: Raw JSON string from config: ' . $raw_json);

        // Áp dụng stripslashes để loại bỏ các backslash thừa trước khi decode JSON
        $stripped_json = stripslashes($raw_json);
        // Log chuỗi JSON sau khi stripslashes
        error_log('ChoAcc.com Sheets Debug: Stripped JSON string before decode: ' . $stripped_json);

        $credentials = json_decode($stripped_json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('ChoAcc.com Sheets Error: Lỗi giải mã JSON credentials: ' . json_last_error_msg() . ' (JSON string: ' . $stripped_json . ')');
            return null;
        }

        // Kiểm tra các trường bắt buộc trong credentials
        $required_fields = ['type', 'project_id', 'private_key', 'client_email'];
        foreach ($required_fields as $field) {
            if (!isset($credentials[$field]) || empty($credentials[$field])) {
                error_log('ChoAcc.com Sheets Error: Thiếu trường bắt buộc trong credentials: ' . $field);
                return null;
            }
        }

        // Log thông tin service account để debug (không log private key)
        error_log('ChoAcc.com Sheets Debug: Service Account Email: ' . $credentials['client_email']);
        error_log('ChoAcc.com Sheets Debug: Project ID: ' . $credentials['project_id']);

        $client->setAuthConfig($credentials);

        // Cấu hình HttpClient với timeout và SSL/TLS
        $client_options = [
            'timeout' => 30,
            'connect_timeout' => 10,
        ];

        // Đường dẫn đến CA certificate bundle
        $ca_cert_path = CHOACC_SHEETS_PLUGIN_DIR . 'certs/cacert.pem';
        
        // Cấu hình xác minh SSL: Ưu tiên disable_ssl_verify, sau đó đến cacert.pem, cuối cùng là mặc định Guzzle (true)
        if ($config['disable_ssl_verify']) {
            $client_options['verify'] = false; // BUỘC TẮT hoàn toàn xác minh SSL
            error_log('ChoAcc.com Sheets WARNING: Xác minh SSL đã TẮT theo cấu hình. KHÔNG NÊN DÙNG TRONG MÔI TRƯỜNG THẬT!');
        } elseif (file_exists($ca_cert_path)) {
            $client_options['verify'] = $ca_cert_path;
            error_log('ChoAcc.com Sheets Debug: Sử dụng CA cert bundle từ: ' . $ca_cert_path);
        } else {
            // Mặc định Guzzle sẽ cố gắng sử dụng CA bundle của hệ thống nếu 'verify' không được đặt hoặc là true.
            // Nếu vẫn lỗi, có thể môi trường PHP/cURL không tìm thấy CA bundle của hệ thống.
            $client_options['verify'] = true; 
            error_log('ChoAcc.com Sheets Warning: cacert.pem không tìm thấy tại ' . $ca_cert_path . '. Xác minh SSL BẬT, dựa vào cấu hình hệ thống PHP.');
        }

        $client->setHttpClient(new \GuzzleHttp\Client($client_options));

    } catch (Exception $e) {
        error_log('ChoAcc.com Sheets Error: Lỗi xác thực Google Client: ' . $e->getMessage());
        return null;
    }

    return new Google_Service_Sheets($client);
}

/**
 * Hàm chẩn đoán chi tiết kết nối Google Sheets API
 */
function choacc_sheets_detailed_diagnosis() {
    $config = choacc_sheets_get_config();
    $diagnosis = [];

    // 1. Kiểm tra thư viện Google API Client
    if (!class_exists('Google_Client')) {
        $diagnosis[] = '❌ Thư viện Google API Client không được tải. Chạy composer install.';
        return $diagnosis;
    }
    $diagnosis[] = '✅ Thư viện Google API Client đã được tải.';

    // 2. Kiểm tra cấu hình cơ bản
    if (empty($config['spreadsheet_id'])) {
        $diagnosis[] = '❌ Google Sheet ID chưa được cấu hình.';
    } else {
        $diagnosis[] = '✅ Google Sheet ID: ' . esc_html(substr($config['spreadsheet_id'], 0, 10)) . '...';
    }

    if (empty($config['service_account_credentials_json'])) {
        $diagnosis[] = '❌ JSON Credentials chưa được cấu hình.';
        return $diagnosis;
    }

    // 3. Kiểm tra JSON Credentials
    $raw_json = $config['service_account_credentials_json'];
    $stripped_json = stripslashes($raw_json); // Áp dụng stripslashes cho mục đích chẩn đoán

    $diagnosis[] = '💡 Chuỗi JSON thô (từ database): <pre style="white-space: pre-wrap; word-break: break-all;">' . esc_html($raw_json) . '</pre>';
    $diagnosis[] = '💡 Chuỗi JSON sau stripslashes: <pre style="white-space: pre-wrap; word-break: break-all;">' . esc_html($stripped_json) . '</pre>';

    $credentials = json_decode($stripped_json, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $diagnosis[] = '❌ JSON Credentials không hợp lệ: ' . json_last_error_msg();
        $diagnosis[] = '💡 Vui lòng kiểm tra lại JSON gốc từ Google Cloud và dán lại vào trường cài đặt.';
        return $diagnosis;
    }
    $diagnosis[] = '✅ JSON Credentials hợp lệ.';

    // 4. Kiểm tra các trường bắt buộc
    $required_fields = ['type', 'project_id', 'private_key', 'client_email'];
    foreach ($required_fields as $field) {
        if (!isset($credentials[$field]) || empty($credentials[$field])) {
            $diagnosis[] = '❌ Thiếu trường bắt buộc: ' . $field;
        } else {
            if ($field === 'client_email') {
                $diagnosis[] = '✅ Service Account Email: ' . esc_html($credentials[$field]);
            } elseif ($field === 'project_id') {
                $diagnosis[] = '✅ Project ID: ' . esc_html($credentials[$field]);
            } else {
                $diagnosis[] = '✅ Trường ' . esc_html($field) . ' có sẵn.';
            }
        }
    }

    // 5. Kiểm tra sự tồn tại của cacert.pem
    $ca_cert_path = CHOACC_SHEETS_PLUGIN_DIR . 'certs/cacert.pem';
    if (file_exists($ca_cert_path)) {
        $diagnosis[] = '✅ Tệp cacert.pem được tìm thấy tại: ' . esc_html($ca_cert_path);
    } else {
        $diagnosis[] = '❌ Tệp cacert.pem KHÔNG tìm thấy tại: ' . esc_html($ca_cert_path) . '. Vui lòng tải xuống và đặt vào thư mục này.';
    }

    // 6. Kiểm tra cấu hình SSL/TLS của PHP
    $diagnosis[] = '--- Cấu hình SSL/TLS của PHP ---';
    $curl_cainfo = ini_get('curl.cainfo');
    $openssl_cafile = ini_get('openssl.cafile');
    $openssl_capath = ini_get('openssl.capath');

    $diagnosis[] = '➡️ curl.cainfo: ' . (empty($curl_cainfo) ? 'Không đặt' : esc_html($curl_cainfo));
    $diagnosis[] = '➡️ openssl.cafile: ' . (empty($openssl_cafile) ? 'Không đặt' : esc_html($openssl_cafile));
    $diagnosis[] = '➡️ openssl.capath: ' . (empty($openssl_capath) ? 'Không đặt' : esc_html($openssl_capath));

    // Kiểm tra xem các đường dẫn này có tồn tại không
    if (!empty($curl_cainfo) && !file_exists($curl_cainfo)) {
        $diagnosis[] = '❌ curl.cainfo chỉ đến tệp không tồn tại.';
    }
    if (!empty($openssl_cafile) && !file_exists($openssl_cafile)) {
        $diagnosis[] = '❌ openssl.cafile chỉ đến tệp không tồn tại.';
    }
    if (!empty($openssl_capath) && !is_dir($openssl_capath)) {
        $diagnosis[] = '❌ openssl.capath chỉ đến thư mục không tồn tại.';
    }
    $diagnosis[] = '--- Kết thúc cấu hình SSL/TLS của PHP ---';


    // 7. Thử tạo Google Client
    try {
        $client = new Google_Client();
        $client->setApplicationName('WooCommerce Google Sheets Integration');
        $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);
        $client->setAuthConfig($credentials);
        
        // Cấu hình HttpClient với cacert.pem hoặc tắt xác minh cho mục đích chẩn đoán
        $client_options_diag = [
            'timeout' => 30,
            'connect_timeout' => 10,
        ];
        
        if ($config['disable_ssl_verify']) {
            $client_options_diag['verify'] = false; 
            $diagnosis[] = '⚠️ Xác minh SSL TẮT cho chẩn đoán theo cấu hình.';
        } elseif (file_exists($ca_cert_path)) {
            $client_options_diag['verify'] = $ca_cert_path;
            $diagnosis[] = '✅ Xác minh SSL BẬT, sử dụng cacert.pem từ plugin.';
        } else {
            // Nếu không tìm thấy cacert.pem và không tắt xác minh, Guzzle sẽ dựa vào cấu hình hệ thống/PHP
            $client_options_diag['verify'] = true; // Mặc định Guzzle
            $diagnosis[] = '⚠️ Xác minh SSL BẬT, dựa vào cấu hình hệ thống PHP (cacert.pem không tìm thấy trong plugin).';
        }
        $client->setHttpClient(new \GuzzleHttp\Client($client_options_diag));


        $diagnosis[] = '✅ Google Client được tạo thành công.';

        // 8. Thử tạo Sheets Service
        $service = new Google_Service_Sheets($client);
        $diagnosis[] = '✅ Google Sheets Service được tạo thành công.';

        // 9. Thử truy cập Google Sheet
        if (!empty($config['spreadsheet_id'])) {
            try {
                $spreadsheet = $service->spreadsheets->get($config['spreadsheet_id']);
                $diagnosis[] = '✅ Có thể truy cập Google Sheet: ' . esc_html($spreadsheet->getProperties()->getTitle());

                // 10. Liệt kê các tab
                $sheets = $spreadsheet->getSheets();
                $diagnosis[] = '📋 Các tab có sẵn:';
                foreach ($sheets as $sheet) {
                    $diagnosis[] = '   - ' . esc_html($sheet->getProperties()->getTitle());
                }

                // 11. Thử đọc dữ liệu từ tab đầu tiên
                if (!empty($sheets)) {
                    $firstSheetName = $sheets[0]->getProperties()->getTitle();
                    try {
                        $range = $firstSheetName . '!A1:G10'; // Đọc vài hàng đầu tiên để kiểm tra
                        $response = $service->spreadsheets_values->get($config['spreadsheet_id'], $range);
                        $values = $response->getValues();
                        if ($values) {
                            $diagnosis[] = '✅ Có thể đọc dữ liệu từ tab "' . esc_html($firstSheetName) . '" (' . count($values) . ' hàng).';
                            if (isset($values[0])) {
                                $diagnosis[] = '📄 Header (10 ký tự đầu): ' . esc_html(substr(implode(', ', $values[0]), 0, 100)) . '...';
                            }
                        } else {
                            $diagnosis[] = '⚠️ Tab "' . esc_html($firstSheetName) . '" không có dữ liệu.';
                        }
                    } catch (Exception $e) {
                        $diagnosis[] = '❌ Không thể đọc dữ liệu từ tab "' . esc_html($firstSheetName) . '": ' . esc_html($e->getMessage());
                    }
                }

            } catch (Google_Service_Exception $e) {
                $diagnosis[] = '❌ Lỗi Google API: ' . esc_html($e->getMessage());
                $diagnosis[] = '💡 Có thể do:';
                $diagnosis[] = '   - Service Account chưa được chia sẻ quyền truy cập vào Sheet';
                $diagnosis[] = '   - Google Sheet ID không chính xác';
                $diagnosis[] = '   - Google Sheets API chưa được kích hoạt trong Google Cloud Console';
                $diagnosis[] = '   - Lỗi SSL/TLS (nếu cacert.pem không đúng hoặc môi trường PHP có vấn đề)';
            } catch (Exception $e) {
                $diagnosis[] = '❌ Lỗi không xác định: ' . esc_html($e->getMessage());
            }
        }

    } catch (Exception $e) {
        $diagnosis[] = '❌ Không thể tạo Google Client: ' . esc_html($e->getMessage());
    }

    return $diagnosis;
}


/**
 * Hàm tìm và lấy một tài khoản cụ thể từ Google Sheet dựa trên ID và tên tab
 * @param string $sheet_name Tên tab (trang tính) trong Google Sheet
 * @param string $account_id_from_product ID tài khoản từ trường tùy chỉnh sản phẩm WooCommerce
 * @return array|null Mảng chứa thông tin tài khoản và số hàng, hoặc null nếu không tìm thấy
 */
function choacc_sheets_find_specific_account($sheet_name, $account_id_from_product) {
    $service = choacc_sheets_get_sheets_service();
    if ( ! $service ) {
        return null;
    }

    $config = choacc_sheets_get_config();
    $spreadsheetId = $config['spreadsheet_id'];
    $idColIndex = $config['columns']['ID']; // Lấy index của cột ID
    $statusColIndex = $config['columns']['Status']; // Lấy index của cột Status

    // Phạm vi đọc dữ liệu (ví dụ: A:G để đọc tất cả các cột liên quan)
    $range = $sheet_name . '!A:G'; // Điều chỉnh phạm vi nếu bạn có nhiều cột hơn

    try {
        $response = $service->spreadsheets_values->get($spreadsheetId, $range);
        $values = $response->getValues();

        if (empty($values)) {
            error_log('ChoAcc.com Sheets Info: Không có dữ liệu trong tab "' . $sheet_name . '" của Google Sheet.');
            return null;
        }

        // Bỏ qua hàng tiêu đề
        $header = array_shift($values); 

        $account_data = null;
        $row_index = 1; // Bắt đầu từ hàng 2 trong Sheet (index 1 trong mảng $values sau khi bỏ header)

        foreach ($values as $row) {
            $row_index++; // Tăng chỉ số hàng trong Google Sheet (để khớp với số hàng trong Sheet)
            // Đảm bảo cột ID và Status tồn tại trong hàng
            if (isset($row[$idColIndex]) && $row[$idColIndex] === $account_id_from_product &&
                isset($row[$statusColIndex]) && $row[$statusColIndex] === $config['status_available']) {
                
                // Lấy thông tin tài khoản dựa trên index cột đã định nghĩa
                $account_data = [
                    'username'  => isset($row[$config['columns']['Username']]) ? $row[$config['columns']['Username']] : '',
                    'password'  => isset($row[$config['columns']['Password']]) ? $row[$config['columns']['Password']] : '',
                    'login_url' => isset($row[$config['columns']['Login_URL']]) ? $row[$config['columns']['Login_URL']] : '',
                    'row_number' => $row_index, // Số hàng trong Google Sheet
                ];
                break; // Tìm thấy tài khoản phù hợp, thoát vòng lặp
            }
        }

        if ( ! $account_data ) {
            error_log('ChoAcc.com Sheets Info: Không tìm thấy tài khoản với ID "' . $account_id_from_product . '" hoặc tài khoản không "Available" trong tab "' . $sheet_name . '".');
        }

        return $account_data;

    } catch (Google_Service_Exception $e) {
        error_log('ChoAcc.com Sheets API Error (find_specific_account on tab ' . $sheet_name . '): ' . $e->getMessage());
        return null;
    } catch (Exception $e) {
        error_log('ChoAcc.com Sheets General Error (find_specific_account on tab ' . $sheet_name . '): ' . $e->getMessage());
        return null;
    }
}

/**
 * Hàm cập nhật trạng thái tài khoản trong Google Sheet
 * @param string $sheet_name Tên tab (trang tính) trong Google Sheet
 * @param int $row_number Số hàng cần cập nhật trong Google Sheet
 * @param string $order_id ID đơn hàng WooCommerce
 * @return bool True nếu cập nhật thành công, False nếu thất bại
 */
function choacc_sheets_update_account_status($sheet_name, $row_number, $order_id) {
    $service = choacc_sheets_get_sheets_service();
    if ( ! $service ) {
        return false;
    }

    $config = choacc_sheets_get_config();
    $spreadsheetId = $config['spreadsheet_id'];
    $statusColIndex = $config['columns']['Status'];
    $orderIdColIndex = $config['columns']['Order_ID'];
    $soldDateColIndex = $config['columns']['Sold_Date'];

    // Chuyển đổi index cột thành ký tự cột (A=0, B=1, C=2...)
    $statusColChar = chr(65 + $statusColIndex);
    $orderIdColChar = chr(65 + $orderIdColIndex);
    $soldDateColChar = chr(65 + $soldDateColIndex);

    // Phạm vi cập nhật (ví dụ: E2 cho cột Status ở hàng 2)
    $range_status = $sheet_name . '!' . $statusColChar . $row_number;
    $range_order_id = $sheet_name . '!' . $orderIdColChar . $row_number;
    $range_sold_date = $sheet_name . '!' . $soldDateColChar . $row_number;

    $values_status = [
        [$config['status_sold']]
    ];
    $values_order_id = [
        [$order_id]
    ];
    $values_sold_date = [
        [current_time('mysql')] // Sử dụng định dạng ngày giờ của WordPress
    ];

    $body_status = new Google_Service_Sheets_ValueRange(['values' => $values_status]);
    $body_order_id = new Google_Service_Sheets_ValueRange(['values' => $values_order_id]);
    $body_sold_date = new Google_Service_Sheets_ValueRange(['values' => $values_sold_date]);

    $params = ['valueInputOption' => 'RAW'];

    try {
        // Cập nhật trạng thái
        $service->spreadsheets_values->update($spreadsheetId, $range_status, $body_status, $params);
        // Cập nhật ID đơn hàng
        $service->spreadsheets_values->update($spreadsheetId, $range_order_id, $body_order_id, $params);
        // Cập nhật ngày bán
        $service->spreadsheets_values->update($spreadsheetId, $range_sold_date, $body_sold_date, $params);
        return true;
    } catch (Google_Service_Exception $e) {
        error_log('ChoAcc.com Sheets API Error (update_account_status on tab ' . $sheet_name . '): ' . $e->getMessage());
        return false;
    } catch (Exception $e) {
        error_log('ChoAcc.com Sheets General Error (update_account_status on tab ' . $sheet_name . '): ' . $e->getMessage());
        return false;
    }
}

/**
 * Template HTML cho email gửi khách hàng
 */
function choacc_sheets_get_email_template_html($data) {
    $config = choacc_sheets_get_config();
    
    $username = isset($data['username']) ? $data['username'] : 'Error';
    $password = isset($data['password']) ? $data['password'] : 'Error';
    $login_url = isset($data['login_url']) ? $data['login_url'] : $config['support_url']; // Nếu không có link đăng nhập, dùng link hỗ trợ
    $special_message = isset($data['special_message']) ? $data['special_message'] : '';
    $call_to_action = isset($data['call_to_action']) ? $data['call_to_action'] : 'Đăng nhập ngay';
    $button_text = isset($data['button_text']) ? $data['button_text'] : 'Đăng nhập ngay';

    $template = '
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thông tin Tài khoản của bạn - ' . esc_html($config['shop_name']) . '</title>
        <!-- Tailwind CSS CDN -->
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            /* Custom font for better aesthetics */
            body {
                font-family: \'Inter\', sans-serif;
                background-color: #f0f2f5; /* Light gray background */
            }
        </style>
    </head>
    <body class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white p-8 rounded-xl shadow-lg w-full max-w-2xl text-center">
            <div class="mb-8">
                <img src="' . esc_url($config['shop_logo_url']) . '" alt="' . esc_attr($config['shop_name']) . '" class="mx-auto rounded-md" style="max-width: 150px; height: auto;">
                <h1 class="text-4xl font-bold text-gray-800 mt-4">' . esc_html($config['shop_name']) . '</h1>
            </div>

            <h2 class="text-2xl font-semibold text-gray-700 mb-6">Chào mừng bạn đến với hệ thống của chúng tôi!</h2>
            <p class="text-gray-600 mb-6">
                Tài khoản bán hàng tự động của bạn đã được tạo thành công. Dưới đây là thông tin đăng nhập của bạn:
            </p>

            <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-800 p-6 rounded-lg mb-8 text-left">
                <p class="mb-2"><strong>Tên đăng nhập (Username):</strong> <span class="font-mono text-lg text-blue-700">' . esc_html($username) . '</span></p>
                <p><strong>Mật khẩu (Password):</strong> <span class="font-mono text-lg text-blue-700">' . esc_html($password) . '</span></p>
                <p class="mt-4 text-sm text-blue-700">
                    <em>' . esc_html($special_message) . '</em>
                </p>
            </div>

            <p class="text-gray-600 mb-8">
                Bạn có thể ' . esc_html($call_to_action) . ' bằng cách nhấp vào nút dưới đây:
            </p>

            <a href="' . esc_url($login_url) . '"
               class="inline-block py-3 px-8 bg-blue-600 hover:bg-blue-700 text-white text-xl font-bold rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                ' . esc_html($button_text) . '
            </a>

            <p class="text-gray-500 text-sm mt-10">
                Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với bộ phận hỗ trợ của chúng tôi tại
                <a href="' . esc_url($config['support_url']) . '" class="text-blue-600 hover:underline">' . esc_html($config['support_text']) . '</a>.
            </p>
            <p class="text-gray-500 text-sm mt-2">
                Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!
            </p>
            <p class="text-gray-500 text-sm mt-4">
                Trân trọng,<br>
                Đội ngũ ' . esc_html($config['shop_name']) . '
            </p>
        </div>
    </body>
    </html>';

    return $template;
}


/**
 * Hàm chính được kích hoạt khi đơn hàng WooCommerce hoàn thành
 * @param int $order_id ID của đơn hàng WooCommerce
 */
function choacc_sheets_distribute_account_on_order_complete($order_id) {
    // Đảm bảo WooCommerce đã được tải
    if ( ! class_exists( 'WooCommerce' ) ) {
        error_log('ChoAcc.com Sheets Error: WooCommerce không được kích hoạt.');
        return;
    }

    $order = wc_get_order( $order_id );
    $config = choacc_sheets_get_config();
    $product_account_id_meta_key = $config['product_account_id_meta_key'];
    $product_sheet_tab_meta_key = $config['product_sheet_tab_meta_key'];

    // Lấy ID tài khoản và tên tab từ sản phẩm trong đơn hàng
    $account_id_from_product = '';
    $sheet_tab_name_from_product = '';
    $product_in_order_name = ''; // Tên sản phẩm trong đơn hàng
    $customer_email = $order->get_billing_email();

    foreach ( $order->get_items() as $item_id => $item ) {
        $product = $item->get_product();
        if ( $product ) {
            $product_in_order_name = $product->get_name();
            // Lấy ID tài khoản và tên tab từ trường tùy chỉnh của sản phẩm
            $account_id_from_product = get_post_meta( $product->get_id(), $product_account_id_meta_key, true );
            $sheet_tab_name_from_product = get_post_meta( $product->get_id(), $product_sheet_tab_meta_key, true );
            
            // Chỉ xử lý nếu cả hai trường đều được điền
            if ( ! empty( $account_id_from_product ) && ! empty( $sheet_tab_name_from_product ) ) {
                break; // Tìm thấy sản phẩm tài khoản đầu tiên có đủ thông tin, thoát vòng lặp
            }
        }
    }

    $email_subject = sprintf( 'Thông tin tài khoản của bạn từ %s - Đơn hàng #%s', $config['shop_name'], $order_id );
    $email_data = [
        'username'        => 'Error',
        'password'        => 'Error',
        'login_url'       => $config['support_url'],
        'special_message' => 'Rất xin lỗi về sự cố đáng tiếc này!! Vui lòng liên hệ Fanpage để được hỗ trợ sớm nhất!! Chân Thành Xin Lỗi Quý Khách!!',
        'call_to_action'  => 'liên hệ Fanpage',
        'button_text'     => 'Liên hệ Fanpage',
    ];
    $admin_note_content = ''; // Ghi chú cho admin

    if ( empty( $account_id_from_product ) || empty( $sheet_tab_name_from_product ) ) {
        // Trường hợp không có sản phẩm nào trong đơn hàng có đủ thông tin cấu hình
        $admin_note_content = sprintf(
            'Lỗi: Đơn hàng #%s (%s) không chứa sản phẩm tài khoản có đủ ID Google Sheet và Tên Tab được cấu hình. Email lỗi đã được gửi đến khách hàng.',
            $order_id,
            $product_in_order_name
        );
        error_log('ChoAcc.com Sheets Info: ' . $admin_note_content);

    } else {
        // Lấy tài khoản cụ thể từ Google Sheet dựa trên TÊN TAB và ID sản phẩm
        $account_details = choacc_sheets_find_specific_account($sheet_tab_name_from_product, $account_id_from_product);

        if ( $account_details ) {
            // Cập nhật trạng thái tài khoản trong Google Sheet (trên đúng tab)
            $updated = choacc_sheets_update_account_status($sheet_tab_name_from_product, $account_details['row_number'], $order_id);

            if ( $updated ) {
                // Thành công: Chuẩn bị dữ liệu cho email
                $email_data['username'] = $account_details['username'];
                $email_data['password'] = $account_details['password'];
                $email_data['login_url'] = !empty($account_details['login_url']) ? $account_details['login_url'] : $config['support_url'];
                $email_data['special_message'] = 'Để bảo mật, chúng tôi khuyến khích bạn đổi mật khẩu ngay sau khi đăng nhập lần đầu.';
                $email_data['call_to_action'] = 'đăng nhập vào tài khoản của mình';
                $email_data['button_text'] = 'Đăng nhập ngay';

                $admin_note_content = sprintf(
                    'Thành công: Tài khoản đã được phân phối cho đơn hàng #%s (%s) (ID: %s, Tab: %s). Email đã được gửi đến khách hàng.',
                    $order_id,
                    $product_in_order_name,
                    $account_id_from_product,
                    $sheet_tab_name_from_product
                );
                error_log('ChoAcc.com Sheets Success: ' . $admin_note_content);

            } else {
                // Lỗi khi cập nhật trạng thái trong Google Sheet
                $admin_note_content = sprintf(
                    'Lỗi: Không thể cập nhật trạng thái tài khoản trong Google Sheet cho đơn hàng #%s (%s) (ID: %s, Tab: %s). Email lỗi đã được gửi đến khách hàng. Vui lòng kiểm tra log lỗi.',
                    $order_id,
                    $product_in_order_name,
                    $account_id_from_product,
                    $sheet_tab_name_from_product
                );
                error_log('ChoAcc.com Sheets Error: ' . $admin_note_content);
            }
        } else {
            // Không tìm thấy tài khoản có sẵn hoặc không khớp ID trên tab cụ thể
            $admin_note_content = sprintf(
                'Lỗi: Không tìm thấy tài khoản "Available" với ID "%s" trong tab "%s" để phân phối cho đơn hàng #%s (%s). Email lỗi đã được gửi đến khách hàng. Vui lòng kiểm tra Google Sheet của bạn và ID/Tên Tab sản phẩm.',
                $account_id_from_product,
                $sheet_tab_name_from_product,
                $order_id,
                $product_in_order_name
            );
            error_log('ChoAcc.com Sheets Error: ' . $admin_note_content);
        }
    }

    // Gửi email cho khách hàng
    $email_body = choacc_sheets_get_email_template_html($email_data);
    $headers = array('Content-Type: text/html; charset=UTF-8');

    $email_sent = wp_mail( $customer_email, $email_subject, $email_body, $headers );

    if ( ! $email_sent ) {
        $admin_note_content .= "\nLỗi: Không thể gửi email cho khách hàng. Vui lòng kiểm tra cấu hình email của WordPress.";
        error_log('ChoAcc.com Sheets Error: Không thể gửi email cho khách hàng ' . $customer_email . ' cho đơn hàng #' . $order_id);
    }

    // Thêm ghi chú vào đơn hàng (chỉ cho admin)
    $order->add_order_note( $admin_note_content, 0 ); // 0 = false, chỉ hiển thị cho admin
    $order->save(); // Lưu đơn hàng để ghi chú được thêm vào
}

// Hook vào hành động khi trạng thái đơn hàng WooCommerce thay đổi thành "completed"
add_action( 'woocommerce_order_status_completed', 'choacc_sheets_distribute_account_on_order_complete' );


/*
 * =============================================================================
 * ADMIN PAGE FUNCTIONS
 * =============================================================================
 */

// Thêm menu item vào Admin Dashboard
add_action( 'admin_menu', 'choacc_sheets_add_admin_menu' );
function choacc_sheets_add_admin_menu() {
    add_submenu_page(
        'woocommerce', // Parent slug
        'Quản lý Tài khoản Google Sheet', // Page title
        'Tài khoản Google Sheet', // Menu title
        'manage_options', // Capability required
        'choacc-sheets-integration', // Menu slug
        'choacc_sheets_admin_page_content' // Callback function to display the page content
    );
}

/**
 * Hiển thị nội dung trang quản trị
 */
function choacc_sheets_admin_page_content() {
    $config = choacc_sheets_get_config();
    $message = '';
    $message_type = ''; // 'success' or 'error'

    // Xử lý lưu cài đặt chung
    if ( isset( $_POST['choacc_sheets_save_settings'] ) && check_admin_referer( 'choacc_sheets_save_settings_nonce' ) ) {
        $new_settings = array(
            'spreadsheet_id'                   => sanitize_text_field( $_POST['spreadsheet_id'] ),
            // Áp dụng stripslashes trước khi lưu JSON credentials
            'service_account_credentials_json' => stripslashes( sanitize_textarea_field( $_POST['service_account_credentials_json'] ) ), 
            'status_available'                 => sanitize_text_field( $_POST['status_available'] ),
            'status_sold'                      => sanitize_text_field( $_POST['status_sold'] ),
            'shop_name'                        => sanitize_text_field( $_POST['shop_name'] ),
            'shop_logo_url'                    => esc_url_raw( $_POST['shop_logo_url'] ),
            'support_url'                      => esc_url_raw( $_POST['support_url'] ),
            'support_text'                     => sanitize_text_field( $_POST['support_text'] ),
            'disable_ssl_verify'               => isset( $_POST['disable_ssl_verify'] ) ? (bool) $_POST['disable_ssl_verify'] : false,
        );

        // Xử lý cột
        $columns = [];
        foreach ( $_POST['columns'] as $key => $value ) {
            $columns[$key] = absint( $value ); // Đảm bảo là số nguyên dương
        }
        $new_settings['columns'] = $columns;

        update_option( 'choacc_sheets_settings', $new_settings );
        $message = 'Cài đặt đã được lưu thành công!';
        $message_type = 'success';
    }

    // Xử lý lưu liên kết sản phẩm
    if ( isset( $_POST['choacc_sheets_save_product_links'] ) && check_admin_referer( 'choacc_sheets_save_product_links_nonce' ) ) {
        $product_ids = isset( $_POST['product_ids'] ) ? array_map( 'absint', $_POST['product_ids'] ) : [];
        $account_ids = isset( $_POST['account_id'] ) ? array_map( 'sanitize_text_field', $_POST['account_id'] ) : [];
        $tab_names = isset( $_POST['tab_name'] ) ? array_map( 'sanitize_text_field', $_POST['tab_name'] ) : [];

        foreach ( $product_ids as $product_id ) {
            $current_account_id = isset( $account_ids[ $product_id ] ) ? $account_ids[ $product_id ] : '';
            $current_tab_name = isset( $tab_names[ $product_id ] ) ? $tab_names[ $product_id ] : '';

            update_post_meta( $product_id, $config['product_account_id_meta_key'], $current_account_id );
            update_post_meta( $product_id, $config['product_sheet_tab_meta_key'], $current_tab_name );
        }
        $message = 'Liên kết sản phẩm đã được lưu thành công!';
        $message_type = 'success';
    }

    // Xử lý kiểm tra kết nối API
    if ( isset( $_POST['choacc_sheets_test_connection'] ) && check_admin_referer( 'choacc_sheets_test_connection_nonce' ) ) {
        $service = choacc_sheets_get_sheets_service();
        if ( $service ) {
            try {
                $spreadsheetId = $config['spreadsheet_id'];
                // Thử đọc một ô bất kỳ để kiểm tra kết nối
                // Lấy tên tab đầu tiên trong Sheet nếu có, hoặc dùng một tên mặc định
                $first_sheet_name = 'Sheet1'; // Mặc định
                try {
                    $spreadsheet_properties = $service->spreadsheets->get($spreadsheetId)->getSheets();
                    if (!empty($spreadsheet_properties)) {
                        $first_sheet_name = $spreadsheet_properties[0]->getProperties()->getTitle();
                    }
                } catch (Exception $e) {
                    // Bỏ qua lỗi nếu không thể lấy tên sheet, dùng mặc định
                }

                $range = $first_sheet_name . '!A1';
                $response = $service->spreadsheets_values->get($spreadsheetId, $range);
                $message = 'Kết nối Google Sheets API thành công! Có thể đọc dữ liệu từ Sheet.';
                $message_type = 'success';
            } catch (Google_Service_Exception $e) {
                $message = 'Lỗi kết nối Google Sheets API: ' . $e->getMessage();
                $message_type = 'error';
            } catch (Exception $e) {
                $message = 'Lỗi không xác định khi kiểm tra kết nối: ' . $e->getMessage();
                $message_type = 'error';
            }
        } else {
            $message = 'Không thể khởi tạo dịch vụ Google Sheets. Vui lòng kiểm tra JSON Credentials và Sheet ID.';
            $message_type = 'error';
        }
    }

    // Xử lý chẩn đoán chi tiết
    if ( isset( $_POST['choacc_sheets_detailed_diagnosis'] ) && check_admin_referer( 'choacc_sheets_detailed_diagnosis_nonce' ) ) {
        $diagnosis_results = choacc_sheets_detailed_diagnosis();
        $message = '<strong>Kết quả chẩn đoán chi tiết:</strong><br><br>' . implode('<br>', $diagnosis_results);
        $message_type = 'info';
    }

    // Xử lý kiểm tra liên kết sản phẩm cụ thể (qua AJAX)
    if ( isset( $_POST['action'] ) && $_POST['action'] === 'choacc_sheets_test_product_link_ajax' ) {
        check_ajax_referer( 'choacc_sheets_test_product_link_nonce', 'security' );
        $product_id = absint( $_POST['product_id'] );
        $account_id = sanitize_text_field( $_POST['account_id'] );
        $tab_name = sanitize_text_field( $_POST['tab_name'] );

        if ( empty( $account_id ) || empty( $tab_name ) ) {
            wp_send_json_error( 'Vui lòng điền đầy đủ ID tài khoản và Tên tab.' );
        }

        $account_details = choacc_sheets_find_specific_account( $tab_name, $account_id );
        if ( $account_details ) {
            wp_send_json_success( 'Tìm thấy tài khoản: ' . esc_html($account_details['username']) . ' (Hàng ' . esc_html($account_details['row_number']) . '). Trạng thái: ' . esc_html($config['status_available']) );
        } else {
            wp_send_json_error( 'Không tìm thấy tài khoản "Available" với ID "' . esc_html($account_id) . '" trong tab "' . esc_html($tab_name) . '". Vui lòng kiểm tra lại.' );
        }
        wp_die();
    }

    // Lấy cài đặt hiện tại để hiển thị trong form
    $current_settings = choacc_sheets_get_config();

    // Lấy danh sách sản phẩm WooCommerce
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => -1, // Lấy tất cả sản phẩm
        'orderby'        => 'title',
        'order'          => 'ASC',
        'fields'         => 'ids', // Chỉ lấy ID để tối ưu
    );
    $product_ids = get_posts( $args );
    ?>
    <div class="wrap">
        <h1>Quản lý Tài khoản Google Sheet cho ChoAcc.com</h1>

        <?php if ( $message ) : ?>
            <div class="notice notice-<?php echo esc_attr( $message_type ); ?> is-dismissible">
                <p><?php
                    if ( $message_type === 'info' ) {
                        echo wp_kses_post( $message ); // Cho phép HTML cho thông báo chẩn đoán
                    } else {
                        echo esc_html( $message );
                    }
                ?></p>
            </div>
        <?php endif; ?>

        <h2 class="title">Cài đặt Chung</h2>
        <form method="post" action="">
            <?php wp_nonce_field( 'choacc_sheets_save_settings_nonce' ); ?>
            <table class="form-table">
                <tbody>
                    <tr>
                        <th scope="row"><label for="spreadsheet_id">Google Sheet ID</label></th>
                        <td>
                            <input name="spreadsheet_id" type="text" id="spreadsheet_id" value="<?php echo esc_attr( $current_settings['spreadsheet_id'] ); ?>" class="regular-text" placeholder="ID của Google Sheet của bạn">
                            <p class="description">Bạn có thể tìm thấy ID này trong URL của Google Sheet (ví dụ: <code>https://docs.google.com/spreadsheets/d/<span style="color:red;">YOUR_SHEET_ID</span>/edit</code>)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="service_account_credentials_json">JSON Service Account Credentials</label></th>
                        <td>
                            <textarea name="service_account_credentials_json" id="service_account_credentials_json" rows="10" cols="70" class="large-text code" placeholder="Dán nội dung JSON của tài khoản dịch vụ vào đây"><?php echo esc_textarea( $current_settings['service_account_credentials_json'] ); ?></textarea>
                            <p class="description">Dán toàn bộ nội dung của tệp JSON bạn đã tải xuống từ Google Cloud Console vào đây.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Chỉ số cột trong Google Sheet (bắt đầu từ 0 cho cột A)</th>
                        <td>
                            <?php foreach ( $current_settings['columns'] as $col_name => $col_index ) : ?>
                                <p>
                                    <label for="column_<?php echo esc_attr( $col_name ); ?>"><?php echo esc_html( $col_name ); ?>:</label>
                                    <input name="columns[<?php echo esc_attr( $col_name ); ?>]" type="number" id="column_<?php echo esc_attr( $col_name ); ?>" value="<?php echo esc_attr( $col_index ); ?>" class="small-text">
                                </p>
                            <?php endforeach; ?>
                            <p class="description">Đảm bảo các chỉ số này khớp với vị trí cột trong Google Sheet của bạn (A=0, B=1, C=2...).</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="status_available">Trạng thái "Còn hàng"</label></th>
                        <td>
                            <input name="status_available" type="text" id="status_available" value="<?php echo esc_attr( $current_settings['status_available'] ); ?>" class="regular-text">
                            <p class="description">Giá trị trong cột Trạng thái của Google Sheet khi tài khoản còn hàng.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="status_sold">Trạng thái "Đã bán"</label></th>
                        <td>
                            <input name="status_sold" type="text" id="status_sold" value="<?php echo esc_attr( $current_settings['status_sold'] ); ?>" class="regular-text">
                            <p class="description">Giá trị trong cột Trạng thái của Google Sheet khi tài khoản đã bán.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="shop_name">Tên cửa hàng</label></th>
                        <td>
                            <input name="shop_name" type="text" id="shop_name" value="<?php echo esc_attr( $current_settings['shop_name'] ); ?>" class="regular-text">
                            <p class="description">Tên cửa hàng sẽ hiển thị trong email gửi khách hàng.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="shop_logo_url">URL Logo cửa hàng</label></th>
                        <td>
                            <input name="shop_logo_url" type="url" id="shop_logo_url" value="<?php echo esc_attr( $current_settings['shop_logo_url'] ); ?>" class="regular-text" placeholder="http://yourdomain.com/your-logo.png">
                            <p class="description">URL đầy đủ của logo cửa hàng (sẽ hiển thị trong email).</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="support_url">URL Hỗ trợ / Fanpage</label></th>
                        <td>
                            <input name="support_url" type="url" id="support_url" value="<?php echo esc_attr( $current_settings['support_url'] ); ?>" class="regular-text" placeholder="https://www.facebook.com/yourfanpage">
                            <p class="description">URL liên hệ hỗ trợ hoặc Fanpage của bạn.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="support_text">Văn bản hỗ trợ</label></th>
                        <td>
                            <input name="support_text" type="text" id="support_text" value="<?php echo esc_attr( $current_settings['support_text'] ); ?>" class="regular-text" placeholder="Fanpage của chúng tôi">
                            <p class="description">Văn bản hiển thị cho liên kết hỗ trợ trong email.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Tắt xác minh SSL (Chỉ gỡ lỗi)</th>
                        <td>
                            <input name="disable_ssl_verify" type="checkbox" id="disable_ssl_verify" value="1" <?php checked( $current_settings['disable_ssl_verify'], true ); ?>>
                            <p class="description" style="color: red; font-weight: bold;">
                                CẢNH BÁO: Tắt xác minh SSL là một rủi ro bảo mật nghiêm trọng và KHÔNG NÊN DÙNG TRONG MÔI TRƯỜNG SẢN PHẨM. Chỉ sử dụng để gỡ lỗi khi bạn chắc chắn vấn đề là SSL.
                            </p>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p class="submit">
                <input type="submit" name="choacc_sheets_save_settings" id="submit" class="button button-primary" value="Lưu Cài đặt">
                <input type="submit" name="choacc_sheets_test_connection" id="test_connection" class="button button-secondary" value="Kiểm tra Kết nối API">
            </p>
        </form>

        <!-- Form riêng cho chẩn đoán chi tiết -->
        <form method="post" action="" style="display: inline;">
            <?php wp_nonce_field( 'choacc_sheets_detailed_diagnosis_nonce' ); ?>
            <p class="submit" style="margin-top: 0;">
                <input type="submit" name="choacc_sheets_detailed_diagnosis" id="detailed_diagnosis" class="button button-secondary" value="🔍 Chẩn đoán Chi tiết" style="background-color: #0073aa; color: white;">
            </p>
        </form>

        <hr>

        <h2 class="title">Quản lý Liên kết Tài khoản Sản phẩm</h2>
        <p class="description">Nhập ID tài khoản và Tên tab Google Sheet cho từng sản phẩm WooCommerce. ID tài khoản phải khớp với cột "ID" trong tab đó.</p>

        <form method="post" action="">
            <?php wp_nonce_field( 'choacc_sheets_save_product_links_nonce' ); ?>
            <table class="wp-list-table widefat fixed striped products">
                <thead>
                    <tr>
                        <th scope="col">Sản phẩm (ID)</th>
                        <th scope="col">SKU</th>
                        <th scope="col">Google Sheet Account ID</th>
                        <th scope="col">Google Sheet Tab Name</th>
                        <th scope="col">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( ! empty( $product_ids ) ) : ?>
                        <?php foreach ( $product_ids as $product_id ) :
                            $product = wc_get_product( $product_id );
                            if ( ! $product ) continue;

                            $current_account_id = get_post_meta( $product_id, $config['product_account_id_meta_key'], true );
                            $current_tab_name = get_post_meta( $product_id, $config['product_sheet_tab_meta_key'], true );
                            ?>
                            <tr>
                                <td>
                                    <input type="hidden" name="product_ids[]" value="<?php echo esc_attr( $product_id ); ?>">
                                    <a href="<?php echo esc_url( get_edit_post_link( $product_id ) ); ?>" target="_blank">
                                        <?php echo esc_html( $product->get_name() ); ?> (#<?php echo esc_html( $product_id ); ?>)
                                    </a>
                                </td>
                                <td><?php echo esc_html( $product->get_sku() ); ?></td>
                                <td>
                                    <input type="text" name="account_id[<?php echo esc_attr( $product_id ); ?>]" 
                                           value="<?php echo esc_attr( $current_account_id ); ?>" 
                                           class="choacc-sheets-account-id regular-text" placeholder="ID tài khoản">
                                </td>
                                <td>
                                    <input type="text" name="tab_name[<?php echo esc_attr( $product_id ); ?>]" 
                                           value="<?php echo esc_attr( $current_tab_name ); ?>" 
                                           class="choacc-sheets-tab-name regular-text" placeholder="Tên tab">
                                </td>
                                <td>
                                    <button type="button" class="button button-small choacc-sheets-test-link" 
                                            data-product-id="<?php echo esc_attr( $product_id ); ?>">
                                        Test
                                    </button>
                                    <span class="choacc-sheets-test-result" id="test-result-<?php echo esc_attr( $product_id ); ?>"></span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="5">Không tìm thấy sản phẩm WooCommerce nào.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            <p class="submit">
                <input type="submit" name="choacc_sheets_save_product_links" id="submit_product_links" class="button button-primary" value="Lưu Liên kết Sản phẩm">
            </p>
        </form>
    </div>

    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.choacc-sheets-test-link').on('click', function() {
                var button = $(this);
                var productId = button.data('product-id');
                var accountId = button.closest('tr').find('.choacc-sheets-account-id').val();
                var tabName = button.closest('tr').find('.choacc-sheets-tab-name').val();
                var resultSpan = $('#test-result-' + productId);

                resultSpan.html('<span style="color: gray;">Đang kiểm tra...</span>');
                button.prop('disabled', true); // Vô hiệu hóa nút trong khi kiểm tra

                $.ajax({
                    url: ajaxurl, // ajaxurl được WordPress định nghĩa sẵn
                    type: 'POST',
                    data: {
                        action: 'choacc_sheets_test_product_link_ajax',
                        security: '<?php echo wp_create_nonce( 'choacc_sheets_test_product_link_nonce' ); ?>',
                        product_id: productId,
                        account_id: accountId,
                        tab_name: tabName
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color: green;">' + response.data + '</span>');
                        } else {
                            resultSpan.html('<span style="color: red;">' + response.data + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color: red;">Lỗi không xác định khi kiểm tra.</span>');
                    },
                    complete: function() {
                        button.prop('disabled', false); // Kích hoạt lại nút
                    }
                });
            });
        });
    </script>
    <?php
}

// Xử lý AJAX cho nút test liên kết sản phẩm cụ thể
add_action( 'wp_ajax_choacc_sheets_test_product_link_ajax', 'choacc_sheets_admin_page_content' );

