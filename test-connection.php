<?php
/**
 * <PERSON>ript test kết nối Google Sheets API
 * Chạy script này để kiểm tra kết nối trước khi sử dụng plugin
 */

// Đường dẫn đến thư viện
require_once 'vendor/autoload.php';

// Thông tin cấu hình - THAY ĐỔI CÁC GIÁ TRỊ NÀY
$SPREADSHEET_ID = 'YOUR_GOOGLE_SHEET_ID_HERE'; // Thay bằng ID Google Sheet của bạn
$CREDENTIALS_FILE = 'loyal-network-465105-p1-52f86a090203.json'; // File JSON credentials

echo "=== TEST KẾT NỐI GOOGLE SHEETS API ===\n\n";

// 1. Kiểm tra file credentials
if (!file_exists($CREDENTIALS_FILE)) {
    echo "❌ Không tìm thấy file credentials: $CREDENTIALS_FILE\n";
    exit(1);
}
echo "✅ File credentials tồn tại: $CREDENTIALS_FILE\n";

// 2. <PERSON><PERSON><PERSON> và kiểm tra JSON
$credentialsJson = file_get_contents($CREDENTIALS_FILE);
$credentials = json_decode($credentialsJson, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "❌ Lỗi JSON: " . json_last_error_msg() . "\n";
    exit(1);
}
echo "✅ JSON credentials hợp lệ\n";

// 3. Kiểm tra các trường bắt buộc
$required_fields = ['type', 'project_id', 'private_key', 'client_email'];
foreach ($required_fields as $field) {
    if (!isset($credentials[$field]) || empty($credentials[$field])) {
        echo "❌ Thiếu trường bắt buộc: $field\n";
        exit(1);
    }
}
echo "✅ Tất cả trường bắt buộc đều có sẵn\n";
echo "📧 Service Account Email: " . $credentials['client_email'] . "\n";
echo "🏗️ Project ID: " . $credentials['project_id'] . "\n\n";

// 4. Tạo Google Client
try {
    $client = new Google_Client();
    $client->setApplicationName('Test Google Sheets Connection');
    $client->setScopes([Google_Service_Sheets::SPREADSHEETS]);
    $client->setAuthConfig($credentials);
    
    // Thêm timeout
    $client->setHttpClient(new \GuzzleHttp\Client([
        'timeout' => 30,
        'connect_timeout' => 10,
    ]));
    
    echo "✅ Google Client được tạo thành công\n";
} catch (Exception $e) {
    echo "❌ Lỗi tạo Google Client: " . $e->getMessage() . "\n";
    exit(1);
}

// 5. Tạo Sheets Service
try {
    $service = new Google_Service_Sheets($client);
    echo "✅ Google Sheets Service được tạo thành công\n";
} catch (Exception $e) {
    echo "❌ Lỗi tạo Sheets Service: " . $e->getMessage() . "\n";
    exit(1);
}

// 6. Kiểm tra Google Sheet ID
if (empty($SPREADSHEET_ID) || $SPREADSHEET_ID === 'YOUR_GOOGLE_SHEET_ID_HERE') {
    echo "⚠️ Vui lòng cập nhật SPREADSHEET_ID trong script này\n";
    echo "💡 Lấy ID từ URL Google Sheet: https://docs.google.com/spreadsheets/d/[ID_HERE]/edit\n";
    exit(0);
}

// 7. Thử truy cập Google Sheet
try {
    echo "🔍 Đang kiểm tra quyền truy cập Google Sheet...\n";
    $spreadsheet = $service->spreadsheets->get($SPREADSHEET_ID);
    echo "✅ Có thể truy cập Google Sheet: " . $spreadsheet->getProperties()->getTitle() . "\n";
    
    // 8. Liệt kê các tab
    $sheets = $spreadsheet->getSheets();
    echo "📋 Các tab có sẵn (" . count($sheets) . " tab):\n";
    foreach ($sheets as $sheet) {
        echo "   - " . $sheet->getProperties()->getTitle() . "\n";
    }
    
    // 9. Thử đọc dữ liệu từ tab đầu tiên
    if (!empty($sheets)) {
        $firstSheetName = $sheets[0]->getProperties()->getTitle();
        echo "\n🔍 Đang đọc dữ liệu từ tab '$firstSheetName'...\n";
        
        try {
            $range = $firstSheetName . '!A1:G10';
            $response = $service->spreadsheets_values->get($SPREADSHEET_ID, $range);
            $values = $response->getValues();
            
            if ($values) {
                echo "✅ Đọc được " . count($values) . " hàng dữ liệu\n";
                if (isset($values[0])) {
                    echo "📄 Header: " . implode(' | ', $values[0]) . "\n";
                }
                
                // Hiển thị vài hàng đầu
                echo "\n📊 Dữ liệu mẫu:\n";
                for ($i = 0; $i < min(3, count($values)); $i++) {
                    echo "   Hàng " . ($i + 1) . ": " . implode(' | ', $values[$i]) . "\n";
                }
            } else {
                echo "⚠️ Tab '$firstSheetName' không có dữ liệu\n";
            }
        } catch (Exception $e) {
            echo "❌ Không thể đọc dữ liệu: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎉 KẾT NỐI THÀNH CÔNG! Plugin sẽ hoạt động bình thường.\n";
    
} catch (Google_Service_Exception $e) {
    echo "❌ Lỗi Google API: " . $e->getMessage() . "\n";
    echo "\n💡 Các nguyên nhân có thể:\n";
    echo "   1. Service Account chưa được chia sẻ quyền truy cập vào Sheet\n";
    echo "   2. Google Sheet ID không chính xác\n";
    echo "   3. Google Sheets API chưa được kích hoạt\n";
    echo "\n🔧 Cách khắc phục:\n";
    echo "   1. Mở Google Sheet > Chia sẻ > Thêm: " . $credentials['client_email'] . "\n";
    echo "   2. Cấp quyền 'Editor' cho service account\n";
    echo "   3. Kiểm tra lại Google Sheet ID\n";
} catch (Exception $e) {
    echo "❌ Lỗi không xác định: " . $e->getMessage() . "\n";
}

echo "\n=== KẾT THÚC TEST ===\n";
?>
